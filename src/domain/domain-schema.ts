// Notes:
//
// 1. Here we only define types that are used in the domain and in some cases,
//    their zod schemas, this is the only JS code allowed here
//
// 2. Enums are defined in their own files (we import them here as needed)
//
// 3. We need to prepend some types with Domain to avoid naming collisions with
//    existing ones in the dom api

import type {AnyObject} from "@unlockre/utils-object/dist";
import {z} from "zod";

import type {AffordabilityIncomeCeiling} from "./affordability-income-ceiling";
import {compSetAccessPolicyTypeSchema} from "./comp-set-access-policy-type";
import type {
  CompSetAccessPolicyType,
  compSetAccessPolicyTypes
} from "./comp-set-access-policy-type";
import {compSetPermissionSchema} from "./comp-set-permission";
import type {CompSetPermission} from "./comp-set-permission";
import type {CompSetReviewsProcessStatus} from "./comp-set-reviews-process-status";
import type {ConcessionBenefitAmountType} from "./concession-benefit-amount-type";
import type {ConcessionBenefitKind} from "./concession-benefit-kind";
import type {ConcessionBenefitPeriodUnit} from "./concession-benefit-periodicity-duration";
import type {ConcessionBenefitTargetType} from "./concession-benefit-target-type";
import type {ConcessionBenefitType} from "./concession-benefit-type";
import {housingSegmentSchema} from "./housing-segment";
import type {HousingSegment} from "./housing-segment";
import type {ListingType, listingTypes} from "./listing-type";
import type {OldPropertyAmenityName} from "./old-property-amenity-name";
import type {oldPropertyAmenityTypes} from "./old-property-amenity-type";
import {propertyAmenitySchema} from "./property-amenity";
import type {PropertyAmenity} from "./property-amenity";
import type {PropertyStage} from "./property-stage";
import type {PropertyStrategy} from "./property-strategy";
import {propertyStyleSchema} from "./property-style";
import type {PropertyStyle} from "./property-style";
import type {
  KnownRenovationStatus,
  RenovationStatus
} from "./renovation-status";
import type {SimilarityItemType} from "./similarity-item-type";
import {unitAmenitySchema} from "./unit-amenity";
import type {UnitAmenity} from "./unit-amenity";
import type {UnitGrouping, unitGroupings} from "./unit-grouping";

const addressSchema = z.object({
  city: z.string().min(1, {message: "Required"}),
  state: z.string().min(1, {message: "Required"}),
  street: z.string().min(1, {message: "Required"}),
  zipCode: z.string().min(1, {message: "Required"})
});

type Address = z.infer<typeof addressSchema>;

const domainLocationSchema = z.object({
  latitude: z.number(),
  longitude: z.number()
});

type DomainLocation = z.infer<typeof domainLocationSchema>;

const addCompSetAccessPolicyInputSchema = z.object({
  compSetId: z.string().min(1, {message: "Required"}),
  entityId: z.string().min(1, {message: "Required"}),
  permissions: z.array(compSetPermissionSchema),
  type: compSetAccessPolicyTypeSchema
});

type AddCompSetAccessPolicyInput = z.infer<
  typeof addCompSetAccessPolicyInputSchema
>;

const createDevelopingPropertyInputSchema = z.object({
  address: addressSchema,
  amenities: z.array(propertyAmenitySchema).optional(),
  constructionYear: z.number().optional(),
  housingSegments: z.array(housingSegmentSchema).optional(),
  location: domainLocationSchema,
  name: z.string().min(1, {message: "Required"}),
  style: propertyStyleSchema.optional(),
  stories: z.number().optional(),
  unitsAmenities: z.array(unitAmenitySchema).optional(),
  unitsTotal: z.number()
});

type CreateDevelopingPropertyInput = z.infer<
  typeof createDevelopingPropertyInputSchema
>;

const createCompSetInputSchema = z.object({
  compPropertyIds: z.array(z.string().min(1, {message: "Required"})),
  name: z.string().min(1, {message: "Required"}),
  propertyId: z.string().min(1, {message: "Required"})
});

type CreateCompSetInput = z.infer<typeof createCompSetInputSchema>;

const removeCompSetAccessPolicyInputSchema = z.object({
  id: z.string().min(1, {message: "Required"}),
  compSetId: z.string().min(1, {message: "Required"})
});

type RemoveCompSetAccessPolicyInput = z.infer<
  typeof removeCompSetAccessPolicyInputSchema
>;

const updateCompSetInputSchema = z.object({
  compPropertyIds: z.array(z.string()).optional(),
  id: z.string().min(1, {message: "Required"}),
  name: z.string().optional(),
  propertyAcquisitionDate: z.string().optional()
});

type UpdateCompSetInput = z.infer<typeof updateCompSetInputSchema>;

// TODO: Make bedrooms of type UnitTypeBedroomQuantity
type PartialUnitConfig = {
  bathrooms?: number;
  bedrooms: number;
};

// TODO: Make bedrooms of type UnitTypeBedroomQuantity
type UnitConfig = {
  bathrooms: number;
  bedrooms: number;
};

type ListingOf<
  TListingType extends ListingType,
  TData extends AnyObject
> = TData & {
  askingRent: number;
  availableOn?: string;
  bathrooms?: number;
  bedrooms: number;
  dateFrom: string;
  dateTo: string;
  effectiveRent?: number;
  isActive: boolean;
  propertyId: string;
  squareFootage?: number;
  type: TListingType;
};

type FloorPlanListing = ListingOf<
  typeof listingTypes.floorPlan,
  {
    floorPlan: string;
  }
>;

type UnitListing = ListingOf<
  typeof listingTypes.unit,
  {
    floorPlan?: string;
    unitId: string;
  }
>;

type Listing = FloorPlanListing | UnitListing;

type UnitComparison = {
  propertyId: string;
  similarityScore: number;
  unitId: string;
};

type UnitRentSuggestion = {
  askingRent: number;
  effectiveRent: number;
  unitComparisons: UnitComparison[];
  unitId: string;
};

type Unit = {
  amenities?: string[];
  bathrooms: number;
  bedrooms: number;
  floor?: number;
  floorPlan?: string;
  id: string;
  propertyId: string;
  renovationProbability?: number;
  renovationStatus: RenovationStatus;
  rentSuggestion?: UnitRentSuggestion;
  squareFootage?: number;
};

const propertyCompsFiltersSchema = z.object({
  hasAffordableUnits: z.boolean().optional(),
  housingSegments: z.array(housingSegmentSchema),
  onlyWithRent: z.boolean(),
  maxDistance: z.number(),
  maxStories: z.number().optional(),
  maxUnitSquareFootage: z.number().optional(),
  maxUnitsTotal: z.number().optional(),
  maxYearBuilt: z.number().optional(),
  minDistance: z.number().optional(),
  minStories: z.number().optional(),
  minUnitSquareFootage: z.number().optional(),
  minUnitsTotal: z.number().optional(),
  minYearBuilt: z.number().optional()
});

type PropertyCompsFilters = z.infer<typeof propertyCompsFiltersSchema>;

type ByUnitUnitRentGroup = {
  avgAskingRent: number;
  avgAskingRentPsf?: number;
  avgDaysOnMarket: number;
  avgEffectiveRent: number;
  avgEffectiveRentPsf?: number;
  bathrooms: number;
  bedrooms: number;
  floorPlan: string;
  listingsTotal: number;
  maxAskingRent: number;
  maxAskingRentPsf?: number;
  maxEffectiveRent: number;
  maxEffectiveRentPsf?: number;
  minAskingRent: number;
  minAskingRentPsf?: number;
  minEffectiveRent: number;
  minEffectiveRentPsf?: number;
  squareFootage?: number;
  unitGrouping: typeof unitGroupings.byUnit;
  unitId: string;
};

type ByUnitMixUnitRentGroup = {
  avgAskingRent: number;
  avgAskingRentPsf?: number;
  avgDaysOnMarket: number;
  avgEffectiveRent: number;
  avgEffectiveRentPsf?: number;
  bathrooms: number;
  bedrooms: number;
  listingsTotal: number;
  maxAskingRent: number;
  maxAskingRentPsf?: number;
  maxEffectiveRent: number;
  maxEffectiveRentPsf?: number;
  minAskingRent: number;
  minAskingRentPsf?: number;
  minEffectiveRent: number;
  minEffectiveRentPsf?: number;
  squareFootage?: number;
  unitGrouping: typeof unitGroupings.byUnitMix;
  unitsAvailable: number;
};

type ByPropertyUnitRentGroup = {
  avgAskingRent: number;
  avgAskingRentPsf?: number;
  avgDaysOnMarket: number;
  avgEffectiveRent: number;
  avgEffectiveRentPsf?: number;
  listingsTotal: number;
  maxAskingRent: number;
  maxAskingRentPsf?: number;
  maxEffectiveRent: number;
  maxEffectiveRentPsf?: number;
  minAskingRent: number;
  minAskingRentPsf?: number;
  minEffectiveRent: number;
  minEffectiveRentPsf?: number;
  unitGrouping: typeof unitGroupings.byProperty;
  unitsAvailable: number;
};

type ByBedroomUnitRentGroup = {
  avgAskingRent: number;
  avgAskingRentPsf?: number;
  avgDaysOnMarket: number;
  avgEffectiveRent: number;
  avgEffectiveRentPsf?: number;
  bedrooms: number;
  listingsTotal: number;
  maxAskingRent: number;
  maxAskingRentPsf?: number;
  maxEffectiveRent: number;
  maxEffectiveRentPsf?: number;
  medianAskingRent: number;
  minAskingRent: number;
  minAskingRentPsf?: number;
  minEffectiveRent: number;
  minEffectiveRentPsf?: number;
  squareFootage?: number;
  unitGrouping: typeof unitGroupings.byBedroom;
  unitsAvailable: number;
};

type ByFloorPlanUnitRentGroup = {
  avgAskingRent: number;
  avgAskingRentPsf?: number;
  avgDaysOnMarket: number;
  avgEffectiveRent: number;
  avgEffectiveRentPsf?: number;
  bathrooms: number;
  bedrooms: number;
  floorPlan: string;
  listingsTotal: number;
  maxAskingRent: number;
  maxAskingRentPsf?: number;
  maxEffectiveRent: number;
  maxEffectiveRentPsf?: number;
  medianAskingRent: number;
  minAskingRent: number;
  minAskingRentPsf?: number;
  minEffectiveRent: number;
  minEffectiveRentPsf?: number;
  squareFootage?: number;
  unitGrouping: typeof unitGroupings.byFloorPlan;
  unitsAvailable: number;
  unitsTotal: number;
};

type UnitRentGroupExceptByUnit =
  | ByBedroomUnitRentGroup
  | ByFloorPlanUnitRentGroup
  | ByPropertyUnitRentGroup
  | ByUnitMixUnitRentGroup;

type UnitRentGroup = ByUnitUnitRentGroup | UnitRentGroupExceptByUnit;

type UnitRentSummaryOf<
  TUnitGrouping extends UnitGrouping,
  TUnitRentGroup extends UnitRentGroup
> = {
  dateFrom: string;
  dateTo: string;
  propertyId: string;
  unitGrouping: TUnitGrouping;
  unitRenovationStatus?: KnownRenovationStatus;
  unitRentGroups: TUnitRentGroup[];
};

type ByUnitUnitRentSummary = UnitRentSummaryOf<
  typeof unitGroupings.byUnit,
  ByUnitUnitRentGroup
>;

type ByUnitMixUnitRentSummary = UnitRentSummaryOf<
  typeof unitGroupings.byUnitMix,
  ByUnitMixUnitRentGroup
>;

type ByBedroomUnitRentSummary = UnitRentSummaryOf<
  typeof unitGroupings.byBedroom,
  ByBedroomUnitRentGroup
>;

type ByPropertyUnitRentSummary = UnitRentSummaryOf<
  typeof unitGroupings.byProperty,
  ByPropertyUnitRentGroup
>;

type ByFloorPlanUnitRentSummary = UnitRentSummaryOf<
  typeof unitGroupings.byFloorPlan,
  ByFloorPlanUnitRentGroup
>;

type UnitRentSummary =
  | ByBedroomUnitRentSummary
  | ByFloorPlanUnitRentSummary
  | ByPropertyUnitRentSummary
  | ByUnitMixUnitRentSummary
  | ByUnitUnitRentSummary;

type DomainImage = {
  url: string;
};

type UnknownOldPropertyAmenity = {
  description: string;
  type: typeof oldPropertyAmenityTypes.unknown;
};

type KnownOldPropertyAmenity = {
  name: OldPropertyAmenityName;
  type: typeof oldPropertyAmenityTypes.known;
};

type OldPropertyAmenity = KnownOldPropertyAmenity | UnknownOldPropertyAmenity;

type PropertyPetFeeSummary = {
  depositFee?: number;
  fee?: number;
  petLimit?: number;
  petWeightLimit?: string;
  rentFee?: number;
};

type PropertyPetFeeValues = {
  cat?: number;
  dog?: number;
  other?: number;
};

type GroupedPropertyPetFeeSummary = {
  cat?: PropertyPetFeeSummary;
  dog?: PropertyPetFeeSummary;
  other?: PropertyPetFeeSummary;
};

type PropertyFeeSummary = {
  adminFee?: number;
  amenityFee?: number;
  applicationFee?: number;
  coveredParkingFee?: number;
  garageFee?: number;
  petFees?: GroupedPropertyPetFeeSummary;
  storageFee?: number;
  surfaceLotParkingFee?: number;
};

type ConcessionBenefit = {
  amountType?: ConcessionBenefitAmountType;
  amountValue?: number;
  applicableUnitType?: string[];
  cheaperAdministrativeFee?: boolean;
  cheaperApplicationFee?: boolean;
  cheaperMoveInFee?: boolean;
  cheaperRent?: boolean;
  cheaperSecurityDeposit?: boolean;
  conditionBedrooms?: number[];
  conditionDeadline?: string;
  conditionFloorplans?: string[];
  conditionSelectedEmployees?: boolean;
  conditionSelectedFloorplans?: boolean;
  conditionSelectedUnits?: boolean;
  conditionUnitNames?: string[];
  deadline?: string;
  freeMonthsAmount?: number;
  freeMonthsUntil?: string;
  isRecurrent?: boolean;
  kind?: ConcessionBenefitKind;
  leaseTermMonths?: number[];
  oneTimeDollarsOffAmount?: number;
  oneTimeDollarsOffPercentage?: number;
  periodAmount?: number;
  periodUnit?: ConcessionBenefitPeriodUnit;
  recurringDollarsOffAmount?: number;
  recurringDollarsOffPercentage?: number;
  recurringMonthsTerm?: number;
  targetType?: ConcessionBenefitTargetType;
  type?: ConcessionBenefitType;
  waivedAdministrativeFee?: boolean;
  waivedApplicationFee?: boolean;
  waivedMoveInFee?: boolean;
  waivedSecurityDeposit?: boolean;
};

type Concession = {
  benefits: ConcessionBenefit[];
  dateFrom: string;
  dateTo: string;
  description: string;
  isActive: boolean;
  propertyId: string;
};

type PropertyUnit = {
  bedrooms?: number;
  quantity?: number;
};

type UnitMix = {
  bathrooms?: number;
  bedrooms: number;
  quantity?: number;
};

type PropertyQualityCategory = {
  description: string;
  name: string;
  score: number;
};

type PropertyQualitySummary = {
  categories: PropertyQualityCategory[];
  propertyId: string;
  totalScore: number;
};

type ReviewSummary = {
  rating?: number;
  totalReviews: number;
};

type PropertyReviewSummary = ReviewSummary & {
  propertyId: string;
};

type AffordabilityRequirement = {
  // TODO: Check why this type is string
  annualIncome: string;
  persons: number;
};

type AffordabilityInfo = {
  affordableUnitsQuantity?: number;
  areAffordableUnits: boolean;
  incomeCeiling?: AffordabilityIncomeCeiling;
  requirements?: AffordabilityRequirement[];
};

type Property = {
  address: Address;
  affordabilityInfo: AffordabilityInfo;
  amenities: PropertyAmenity[];
  concessions?: Concession[];
  feeSummary?: PropertyFeeSummary;
  historicalRentSummary?: HistoricalRentSummary;
  housingSegments: HousingSegment[];
  id: string;
  images: DomainImage[];
  // TODO: We should deprecate `legacyUnits`.
  legacyUnits?: PropertyUnit[];
  listings?: Listing[];
  location: DomainLocation;
  managerCompany?: string;
  name?: string;
  occupancy?: number;
  oldAmenities: OldPropertyAmenity[];
  owner?: string;
  phone?: string;
  qualitySummary?: PropertyQualitySummary;
  reviewSummary?: PropertyReviewSummary;
  squareFootage?: number;
  stage: PropertyStage;
  stories?: number;
  strategy?: PropertyStrategy;
  style?: PropertyStyle;
  type?: string;
  unitAmenities: UnitAmenity[];
  unitMixConfidence?: number;
  unitMixes: UnitMix[];
  unitRentSummaries?: UnitRentSummary[];
  units?: Unit[];
  unitsTotal?: number;
  website?: string;
  yearBuilt?: number;
};

type PropertyMatch = {
  matchingScore: number;
  propertyId: string;
};

type PropertyMatchGroup = {
  propertyMatches: PropertyMatch[];
  searchTerm: string;
};

type SimilarityItem = {
  similarity: number;
  type: SimilarityItemType;
  weight: number;
};

type Comp = {
  distance: number;
  property?: Property;
  // We are leaving this one here because the api comps don't have an id
  propertyId: string;
  similarityItems: SimilarityItem[];
  similarityScore: number;
};

type CompSetReviewsProcess = {
  compSetId: string;
  createdAt: string;
  status: CompSetReviewsProcessStatus;
  updatedAt: string;
};

type RelatedOrganization = {
  id: string;
  name: string;
};

type RelatedUser = {
  email: string;
  id: string;
  name: string;
  organization: RelatedOrganization;
};

type CompSetAccessPolicyEntity = RelatedOrganization | RelatedUser;

type CompSetAccessPolicyEntityFrom<
  TCompSetAccessPolicyType extends CompSetAccessPolicyType
> =
  TCompSetAccessPolicyType extends typeof compSetAccessPolicyTypes.organization
    ? RelatedOrganization
    : TCompSetAccessPolicyType extends typeof compSetAccessPolicyTypes.user
      ? RelatedUser
      : never;

type CompSetAccessPolicyOf<
  TType extends CompSetAccessPolicyType,
  TEntity extends CompSetAccessPolicyEntity
> = {
  compSetId: string;
  createdAt: string;
  entity: TEntity;
  id: string;
  permissions: CompSetPermission[];
  type: TType;
};

type OrganizationCompSetAccessPolicy = CompSetAccessPolicyOf<
  typeof compSetAccessPolicyTypes.organization,
  RelatedOrganization
>;

type UserCompSetAccessPolicy = CompSetAccessPolicyOf<
  typeof compSetAccessPolicyTypes.user,
  RelatedUser
>;

type CompSetAccessPolicy =
  | OrganizationCompSetAccessPolicy
  | UserCompSetAccessPolicy;

type CompSetAccessPolicyFrom<
  TCompSetAccessPolicyType extends CompSetAccessPolicyType
> =
  TCompSetAccessPolicyType extends typeof compSetAccessPolicyTypes.organization
    ? OrganizationCompSetAccessPolicy
    : TCompSetAccessPolicyType extends typeof compSetAccessPolicyTypes.user
      ? UserCompSetAccessPolicy
      : never;

type CompSetPermissionGroup = {
  compSetId: string;
  permissions: CompSetPermission[];
};

type CompSet = {
  accessPolicies?: CompSetAccessPolicy[];
  comps?: Comp[];
  compsTotal: number;
  createdAt: string;
  id: string;
  name: string;
  owner?: RelatedUser;
  permissions?: CompSetPermission[];
  property?: Property;
  propertyAcquisitionDate?: string;
  propertyId: string;
  propertyStage: PropertyStage;
  reviewsProcess?: CompSetReviewsProcess;
  updatedAt: string;
};

type RentHistoryItemDataPoint = {
  x: Date;
  y: number;
};

type RentHistory = {
  compsAskingRent: RentHistoryItemDataPoint[];
  compsAskingRentPsf: RentHistoryItemDataPoint[];
  compsEffectiveRent: RentHistoryItemDataPoint[];
  compsEffectiveRentPsf: RentHistoryItemDataPoint[];
  compsEffectiveRentPsfVariation?: number;
  compsEffectiveRentVariation?: number;
  propertyAskingRent: RentHistoryItemDataPoint[];
  propertyAskingRentPsf: RentHistoryItemDataPoint[];
  propertyEffectiveRent: RentHistoryItemDataPoint[];
  propertyEffectiveRentPsf: RentHistoryItemDataPoint[];
  propertyEffectiveRentPsfVariation?: number;
  propertyEffectiveRentVariation?: number;
};

type RentHistoryByBedrooms = {
  one: RentHistory;
  studio: RentHistory;
  three: RentHistory;
  two: RentHistory;
};

type CompSetRentHistory = {
  propertyId: string;
  rentByBedroom: RentHistoryByBedrooms;
  rentByProperty: RentHistory;
};

type HistoricalRent = {
  averageRent: number;
  averageRentPSF?: number;
  dateFrom: string;
  dateTo: string;
  medianRent: number;
  medianRentPSF?: number;
};

type HistoricalRentSummary = {
  askingHistoricalRents: HistoricalRent[];
  askingRentChange?: number;
  effectiveHistoricalRents: HistoricalRent[];
  effectiveRentChange?: number;
  oneBedroomAskingHistoricalRents: HistoricalRent[];
  oneBedroomAskingRentChange?: number;
  oneBedroomEffectiveHistoricalRents: HistoricalRent[];
  oneBedroomEffectiveRentChange?: number;
  propertyId: string;
  studioAskingHistoricalRents: HistoricalRent[];
  studioAskingRentChange?: number;
  studioEffectiveHistoricalRents: HistoricalRent[];
  studioEffectiveRentChange?: number;
  threeBedroomAskingHistoricalRent: HistoricalRent[];
  threeBedroomAskingRentChange?: number;
  threeBedroomEffectiveHistoricalRent: HistoricalRent[];
  threeBedroomEffectiveRentChange?: number;
  twoBedroomAskingHistoricalRent: HistoricalRent[];
  twoBedroomAskingRentChange?: number;
  twoBedroomEffectiveHistoricalRent: HistoricalRent[];
  twoBedroomEffectiveRentChange?: number;
};

export {
  addCompSetAccessPolicyInputSchema,
  createCompSetInputSchema,
  createDevelopingPropertyInputSchema,
  propertyCompsFiltersSchema,
  removeCompSetAccessPolicyInputSchema,
  updateCompSetInputSchema
};
export type {
  AddCompSetAccessPolicyInput,
  Address,
  AffordabilityInfo,
  AffordabilityRequirement,
  ByBedroomUnitRentGroup,
  ByBedroomUnitRentSummary,
  ByPropertyUnitRentSummary,
  ByPropertyUnitRentGroup,
  ByUnitMixUnitRentGroup,
  ByUnitMixUnitRentSummary,
  ByUnitUnitRentGroup,
  ByUnitUnitRentSummary,
  ByFloorPlanUnitRentGroup,
  ByFloorPlanUnitRentSummary,
  Comp,
  CompSet,
  CompSetAccessPolicyEntity,
  CompSetAccessPolicyEntityFrom,
  CompSetAccessPolicy,
  CompSetAccessPolicyFrom,
  CompSetPermissionGroup,
  CompSetReviewsProcess,
  Concession,
  ConcessionBenefit,
  CompSetRentHistory,
  CreateCompSetInput,
  CreateDevelopingPropertyInput,
  DomainImage,
  FloorPlanListing,
  GroupedPropertyPetFeeSummary,
  HistoricalRent,
  HistoricalRentSummary,
  KnownOldPropertyAmenity,
  Listing,
  OrganizationCompSetAccessPolicy,
  PropertyStrategy,
  OldPropertyAmenity,
  PartialUnitConfig,
  Property,
  PropertyAmenity,
  PropertyCompsFilters,
  PropertyFeeSummary,
  PropertyMatch,
  PropertyMatchGroup,
  PropertyPetFeeValues,
  PropertyQualityCategory,
  PropertyQualitySummary,
  PropertyReviewSummary,
  PropertyStyle,
  RelatedOrganization,
  RelatedUser,
  RemoveCompSetAccessPolicyInput,
  ReviewSummary,
  SimilarityItem,
  Unit,
  UnitComparison,
  UnitConfig,
  UnitListing,
  UnitMix,
  UnitRentGroup,
  UnitRentGroupExceptByUnit,
  UnitRentSuggestion,
  UnitRentSummary,
  UnknownOldPropertyAmenity,
  UpdateCompSetInput,
  UserCompSetAccessPolicy,
  RentHistory
};
