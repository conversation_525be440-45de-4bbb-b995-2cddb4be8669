import {groupBy} from "@unlockre/utils-array/dist";
import {
  ensureIsDefined,
  isDefined,
  mapIfDefined
} from "@unlockre/utils-validation/dist";

import * as withAddress from "@/domain/address";
import type {
  GroupedPropertyPetFeeSummary,
  HistoricalRentSummary,
  Property,
  PropertyFeeSummary,
  PropertyReviewSummary,
  UnitRentSummary
} from "@/domain/domain-schema";

import * as withPropertyFeeSummary from "./property-fee-summary";
import type {UnitGrouping} from "./unit-grouping";
import * as withUnitRentSummary from "./unit-rent-summary";

const setReviewSummary = (
  property: Property,
  reviewSummary: PropertyReviewSummary
): Property => ({
  ...property,
  reviewSummary
});

const setHistoricalRentSummary = (
  property: Property,
  historicalRentSummary?: HistoricalRentSummary
): Property => ({
  ...property,
  historicalRentSummary
});

const getFeeSummaries = (properties: Property[]) =>
  properties.map(property => property.feeSummary).filter(isDefined);

const getFeeSummariesAvgPrice = (
  properties: Property[],
  getAveragePrice: (feeSummary: PropertyFeeSummary) => number | undefined
) =>
  withPropertyFeeSummary.getAveragePrice(
    getFeeSummaries(properties),
    getAveragePrice
  );

const getPetFeeSummariesAvgPrice = (
  properties: Property[],
  getAveragePrice: (petFees: GroupedPropertyPetFeeSummary) => number | undefined
) =>
  getFeeSummariesAvgPrice(properties, feeSummary =>
    mapIfDefined(feeSummary.petFees, getAveragePrice)
  );

const getUnitRentSummaryOf = <TUnitGrouping extends UnitGrouping>(
  property: Property,
  unitGrouping: TUnitGrouping
) => {
  const {unitRentSummaries} = property;

  return unitRentSummaries?.length
    ? withUnitRentSummary.findUnitGroupingOf(unitRentSummaries, unitGrouping)
    : undefined;
};

const getTotalUnitsByBedrooms = (property: Property, bedrooms: number) => {
  const unitMixesGroupedByBedrooms = groupBy(property.unitMixes, unitMix =>
    unitMix.bedrooms.toString()
  );

  return unitMixesGroupedByBedrooms[bedrooms]?.reduce(
    (count, unitMixRecord) => count + (unitMixRecord.quantity ?? 0),
    0
  );
};

const formatAddress = (property: Property) =>
  withAddress.format(property.address);

const formatNameOrAddress = (property: Property) =>
  property.name ?? formatAddress(property);

const ensureUnits = (property: Property) =>
  ensureIsDefined(
    property.units,
    "No units in property with id: " + property.id
  );

const ensureUnitRentSummaries = (property: Property) =>
  ensureIsDefined(
    property.unitRentSummaries,
    "No unitRentSummaries in property with id: " + property.id
  );

const ensureReviewSummary = (property: Property) =>
  ensureIsDefined(
    property.reviewSummary,
    "No reviewSummary in property with id: " + property.id
  );

const ensureQualitySummary = (property: Property) =>
  ensureIsDefined(
    property.qualitySummary,
    "No qualitySummary in property with id: " + property.id
  );

const ensureListings = (property: Property) =>
  ensureIsDefined(
    property.listings,
    "No listings in property with id: " + property.id
  );

const addUnitRentSummaries = (
  property: Property,
  unitRentSummaries: UnitRentSummary[]
): Property => ({
  ...property,
  unitRentSummaries: [
    ...(property.unitRentSummaries ?? []),
    ...unitRentSummaries
  ]
});

export {
  addUnitRentSummaries,
  ensureListings,
  ensureQualitySummary,
  ensureReviewSummary,
  ensureUnitRentSummaries,
  ensureUnits,
  formatAddress,
  formatNameOrAddress,
  getUnitRentSummaryOf,
  getFeeSummaries,
  getFeeSummariesAvgPrice,
  getPetFeeSummariesAvgPrice,
  getTotalUnitsByBedrooms,
  setHistoricalRentSummary,
  setReviewSummary
};
