import {z} from "zod";

const propertyStyles = {
  garden: "GARDEN",
  highRise: "HIGH_RISE",
  lowRise: "LOW_RISE",
  midRise: "MID_RISE",
  townhome: "TOWNHOME"
} as const;

type PropertyStyle = (typeof propertyStyles)[keyof typeof propertyStyles];

const allPropertyStyles = [
  propertyStyles.garden,
  propertyStyles.highRise,
  propertyStyles.lowRise,
  propertyStyles.midRise,
  propertyStyles.townhome
] as const;

const propertyStyleSchema = z.enum(allPropertyStyles);

const propertyStyleTitles = {
  [propertyStyles.garden]: "Garden Style",
  [propertyStyles.highRise]: "High-Rise",
  [propertyStyles.lowRise]: "Low-Rise",
  [propertyStyles.midRise]: "Mid-Rise",
  [propertyStyles.townhome]: "Townhome"
} as const;

const getTitle = (propertyStyle: PropertyStyle) =>
  propertyStyleTitles[propertyStyle];

const propertyStyleDescriptions = {
  [propertyStyles.lowRise]:
    "AI analysis suggests this is a low-rise building with a few floors.",
  [propertyStyles.midRise]:
    "AI analysis suggests this is a mid-rise building with multiple floors.",
  [propertyStyles.highRise]:
    "AI analysis suggests this is a high-rise with many floors.",
  [propertyStyles.townhome]:
    "AI analysis suggests this is a townhome with a private entry.",
  [propertyStyles.garden]:
    "AI analysis suggests this is a garden-style property with green spaces."
} as const;

const getDescription = (propertyStyle: PropertyStyle) =>
  propertyStyleDescriptions[propertyStyle];

export {getDescription, getTitle, propertyStyles, propertyStyleSchema};
export type {PropertyStyle};
