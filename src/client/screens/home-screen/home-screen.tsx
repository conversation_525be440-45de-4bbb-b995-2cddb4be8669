import {KeyosPrivatePageSection} from "@unlockre/keyos-tools/dist";
import {LoadingPage} from "@unlockre/page-tools/dist";
import styled from "styled-components";

import {
  KeycompsPageTitle,
  PageContentContainer
} from "@/client/components/page";

import {CompSetsSection} from "./comp-sets-section";
import {DeleteCompSetDialog} from "./delete-comp-set-dialog";
import {Header} from "./header";
import {ShareCompSetDialog} from "./share-comp-set-dialog";
import {useHomeScreen} from "./use-home-screen";

const SectionsContainer = styled(KeyosPrivatePageSection)`
  display: flex;
  flex-direction: column;
  padding-bottom: 48px;
`;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100%;
`;

const HomeScreen = () => {
  const {
    allowSharingCompSets,
    canCreateCompSets,
    clearCompSetToDelete,
    clearCompSetToShare,
    compSets,
    compSetToDelete,
    compSetToShare,
    createCompSet,
    deleteCompSet,
    goToCompSetEditScreen,
    hasCompSets,
    isLoading,
    selectedPropertyMatch,
    setCompSetToDelete,
    setCompSetToShare,
    setSelectedPropertyMatch
  } = useHomeScreen();

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <>
      <KeycompsPageTitle titleSegments={["Home", "Comp Sets"]} />
      <Container>
        <Header
          {...{canCreateCompSets, hasCompSets, selectedPropertyMatch}}
          onCompSetCreate={createCompSet}
          onPropertyMatchSelect={setSelectedPropertyMatch}
        />
        {hasCompSets && (
          <PageContentContainer>
            <SectionsContainer>
              <CompSetsSection
                {...{allowSharingCompSets, canCreateCompSets, compSets}}
                onCompSetDelete={setCompSetToDelete}
                onCompSetEdit={goToCompSetEditScreen}
                onCompSetShare={setCompSetToShare}
              />
            </SectionsContainer>
          </PageContentContainer>
        )}
      </Container>
      {compSetToDelete && (
        <DeleteCompSetDialog
          compSet={compSetToDelete}
          onCompSetDeleteAccept={deleteCompSet}
          onCompSetDeleteCancel={clearCompSetToDelete}
        />
      )}
      <ShareCompSetDialog
        compSetId={compSetToShare?.id}
        onCompSetShareFinish={clearCompSetToShare}
      />
    </>
  );
};

export {HomeScreen};
