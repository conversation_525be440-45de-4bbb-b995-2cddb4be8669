import {useCallback, useState} from "react";

import {useUserSplitIoTreatments} from "@/client/utils/split-io";
import type {FeatureFlagName} from "@/client/utils/split-io/feature-flag-name";
import type {CompSet} from "@/domain/domain-schema";
import type {PropertyMatch} from "@/utils/property-search-api";

import {useCanCreateCompSets} from "./use-can-create-comp-sets";
import {useCompSets} from "./use-comp-sets";
import {useCreateCompSet} from "./use-create-comp-set";
import {useDeleteCompSet} from "./use-delete-comp-set";
import {useGoToCompSetEditScreen} from "./use-go-to-comp-set-edit-screen";

const featureFlagNames = [
  "allowSharingCompSets"
] as const satisfies readonly FeatureFlagName[];

// eslint-disable-next-line max-statements
const useHomeScreen = () => {
  const treatments = useUserSplitIoTreatments(featureFlagNames);

  const allowSharingCompSets =
    treatments?.allowSharingCompSets.treatment === "on";

  const {canCreateCompSets, isCanCreateCompSetsLoading} =
    useCanCreateCompSets();

  const {compSets, hasCompSets, isHasCompSetsLoading, refetchCompSets} =
    useCompSets();

  const [compSetToDelete, setCompSetToDelete] = useState<CompSet | null>(null);

  const clearCompSetToDelete = useCallback(() => {
    setCompSetToDelete(null);
  }, []);

  const [compSetToShare, setCompSetToShare] = useState<CompSet | null>(null);

  const clearCompSetToShare = useCallback(() => {
    setCompSetToShare(null);
  }, []);

  const [selectedPropertyMatch, setSelectedPropertyMatch] =
    useState<PropertyMatch | null>(null);

  const goToCompSetEditScreen = useGoToCompSetEditScreen();

  const createCompSet = useCreateCompSet(goToCompSetEditScreen);

  const onDeleteCompSet = useCallback(() => {
    setCompSetToDelete(null);

    refetchCompSets();
  }, [refetchCompSets, setCompSetToDelete]);

  const deleteCompSet = useDeleteCompSet(onDeleteCompSet);

  const isLoading = isCanCreateCompSetsLoading || isHasCompSetsLoading;

  return {
    allowSharingCompSets,
    canCreateCompSets,
    clearCompSetToDelete,
    clearCompSetToShare,
    compSetToDelete,
    compSetToShare,
    compSets,
    createCompSet,
    deleteCompSet,
    goToCompSetEditScreen,
    hasCompSets,
    isLoading,
    selectedPropertyMatch,
    setCompSetToDelete,
    setCompSetToShare,
    setSelectedPropertyMatch
  };
};

export {useHomeScreen};
