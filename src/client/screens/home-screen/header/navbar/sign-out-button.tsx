import {But<PERSON>} from "@unlockre/components-library/dist/buttons";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {useAuth0Auth} from "@unlockre/utils-auth0/dist";

import {magicVioletApplicationTheme} from "@/client/components/application/application-theme";

import {useTrackHomeEvent} from "../../home-event";

const SignOutButton = () => {
  const {logoutTo} = useAuth0Auth();

  const trackEvent = useTrackHomeEvent();

  const signOut = () => {
    logoutTo();

    trackEvent({
      type: "signOut"
    });
  };

  return (
    <ThemeProvider theme={magicVioletApplicationTheme}>
      <Button onClick={signOut} size="medium" variant="outline">
        Sign Out
      </Button>
    </ThemeProvider>
  );
};

export {SignOutButton};
