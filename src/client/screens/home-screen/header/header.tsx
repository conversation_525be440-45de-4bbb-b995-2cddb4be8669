import {getColorByAlias} from "@unlockre/components-library/dist/theme-provider/theme";
import {KeyosPrivatePageSection} from "@unlockre/keyos-tools/dist";
import type {ComponentProps} from "react";
import styled, {css} from "styled-components";

import {Content} from "./content";
import {Navbar} from "./navbar";
import withCompSetsBackgroundImageSrc from "./with-comp-sets-background.svg";
import withoutCompSetsBackgroundImageSrc from "./without-comp-sets-background.svg";

type ContentSectionProps = ComponentProps<typeof Content>;

type Props = ContentSectionProps & {
  hasCompSets?: boolean;
};

type ContainerStyledProps = {
  $hasCompSets?: boolean;
};

type InnerContainerStyledProps = {
  $hasCompSets?: boolean;
};

const noCompSetsInnerContainerCss = css`
  display: flex;
  flex-direction: column;
  flex: 1;
`;

// prettier-ignore
const InnerContainer = styled(KeyosPrivatePageSection)<InnerContainerStyledProps>`
  ${props => (!props.$hasCompSets && noCompSetsInnerContainerCss)}
`;

const withoutCompSetsContainerCss = css`
  background-image: url("${withoutCompSetsBackgroundImageSrc}");
  display: flex;
  flex-direction: column;
  flex-grow: 1;
`;

const withCompSetsContainerCss = css`
  background-image: url("${withCompSetsBackgroundImageSrc}");
`;

const Container = styled.div<ContainerStyledProps>`
  ${props =>
    props.$hasCompSets ? withCompSetsContainerCss : withoutCompSetsContainerCss}

  background-color: ${getColorByAlias("headerBg")};
  background-repeat: no-repeat;
  background-size: cover;
`;

const Header = ({hasCompSets, ...rest}: Props) => (
  <Container $hasCompSets={hasCompSets}>
    <InnerContainer $hasCompSets={hasCompSets}>
      <Navbar />
      <Content {...{hasCompSets}} {...rest} />
    </InnerContainer>
  </Container>
);

export {Header};
