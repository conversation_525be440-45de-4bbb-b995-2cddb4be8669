import {InfoItem} from "@unlockre/components-library/dist/info-item";
import {PrintingReady} from "@unlockre/components-library/dist/printing";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {
  getColorByAlias,
  getTypography
} from "@unlockre/components-library/dist/theme-provider/theme";
import {useUrlQuery} from "@unlockre/utils-next/dist";
import styled from "styled-components";

import {magicVioletApplicationTheme} from "@/client/components/application/application-theme";
import {trpc} from "@/client/utils/trpc";
import * as withCompSet from "@/domain/comp-set";

import * as withUrlQuery from "../url-query";

import {AcquisitionDate} from "./acquisition-date/acquisition-date";
import {CopyButton} from "./copy-button";
import {EditCompSetButton} from "./edit-comp-set-button";
import {ExportAnalysisButton} from "./export-analysis-button";
import {ExportCustomSummaryButton} from "./export-custom-summary-button";
import {HeaderLayout} from "./header-layout";
import {HeaderSkeleton} from "./header-skeleton";
import {PropertyNameAndAddressInfo} from "./property-name-and-address-info";

const CompSetName = styled.label`
  ${getTypography("title", "l", 600)}

  color: ${getColorByAlias("textInverted")};
`;

// eslint-disable-next-line max-statements
const Header = () => {
  const {urlQuery} = useUrlQuery(withUrlQuery.getFrom);

  const compSetResponse = trpc.getCompSet.useQuery({
    compSetId: urlQuery.compSetId,
    relations: [{name: "permissions"}, {name: "property"}]
  });

  const compSet = compSetResponse.data;

  const isLoading = compSetResponse.isLoading;

  const utils = trpc.useUtils();

  const onAcquisitionDateChange = () => {
    utils.getCompSet.invalidate();
  };

  const property = compSet ? withCompSet.ensureProperty(compSet) : undefined;

  if (isLoading || !compSet) {
    return (
      <PrintingReady isReady={false} name="comp-set-view-screen-header">
        <HeaderSkeleton />
      </PrintingReady>
    );
  }

  return (
    <HeaderLayout
      acquisitionDateElement={
        <AcquisitionDate
          compSet={compSet}
          onAcquisitionDateChange={onAcquisitionDateChange}
        />
      }
      actionButtons={
        <ThemeProvider theme={magicVioletApplicationTheme}>
          <CopyButton
            getTextToCopy={() => window.location.href}
            size="medium"
            variant="transparent"
          >
            Copy Link
          </CopyButton>
          <ExportCustomSummaryButton />
          <ExportAnalysisButton />
          <EditCompSetButton {...{compSet}} />
        </ThemeProvider>
      }
      compSetNameElement={<CompSetName>{compSet?.name}</CompSetName>}
      propertyNameAndAddressElement={
        <PropertyNameAndAddressInfo {...{property}} />
      }
      storiesElement={
        <InfoItem
          label="Stories"
          size="small"
          value={property?.stories}
          variant="light"
        />
      }
      unitsElement={
        <InfoItem
          label="Units"
          size="small"
          value={property?.unitsTotal}
          variant="light"
        />
      }
      yearBuiltElement={
        <InfoItem
          label="Year Built"
          size="small"
          value={property?.yearBuilt}
          variant="light"
        />
      }
    />
  );
};

export {Header};
