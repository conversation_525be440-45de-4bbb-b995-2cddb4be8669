import * as withColumn from "@/domain/column";
import * as withConcessionBenefitPeriodUnit from "@/domain/concession-benefit-periodicity-duration";

import * as withConcessionColumnName from "./concession-column-name";
import type {ConcessionItem} from "./concession-item";

type ConcessionColumn =
  | typeof amountTypeColumn
  | typeof amountValueColumn
  | typeof benefitKindColumn
  | typeof benefitTypeColumn
  | typeof cheaperAdministrativeFeeColumn
  | typeof cheaperApplicationFeeColumn
  | typeof cheaperMoveInFeeColumn
  | typeof cheaperRentColumn
  | typeof cheaperSecurityDepositColumn
  | typeof conditionBedroomsColumn
  | typeof conditionDeadlineColumn
  | typeof conditionFloorplansColumn
  | typeof conditionSelectedEmployeesColumn
  | typeof conditionSelectedFloorplansColumn
  | typeof conditionSelectedUnitsColumn
  | typeof conditionUnitNamesColumn
  | typeof dateFromColumn
  | typeof dateToColumn
  | typeof deadlineColumn
  | typeof descriptionColumn
  | typeof freeMonthsAmountColumn
  | typeof freeMonthsUntilColumn
  | typeof isActiveColumn
  | typeof isRecurrentColumn
  | typeof leaseTermMonthsColumn
  | typeof oneTimeDollarsOffAmountColumn
  | typeof oneTimeDollarsOffPercentageColumn
  | typeof periodColumn
  | typeof propertyNameColumn
  | typeof recurringDollarsOffAmountColumn
  | typeof recurringDollarsOffPercentageColumn
  | typeof recurringMonthsTermColumn
  | typeof waivedAdministrativeFeeColumn
  | typeof waivedApplicationFeeColumn
  | typeof waivedMoveInFeeColumn
  | typeof waivedSecurityDepositColumn;

const getTitle = (concessionColumn: ConcessionColumn) =>
  withConcessionColumnName.getTitle(concessionColumn.name);

const getDescription = (concessionColumn: ConcessionColumn) =>
  withConcessionColumnName.getDescription(concessionColumn.name);

const createWithoutSummary = withColumn.createWithoutSummary<ConcessionItem>();

const {concessionColumnNames} = withConcessionColumnName;

const propertyNameColumn = createWithoutSummary(
  concessionColumnNames.propertyName,
  concessionItem => concessionItem.property.name
);

const descriptionColumn = createWithoutSummary(
  concessionColumnNames.description,
  concessionItem => concessionItem.concession.description
);

const isActiveColumn = createWithoutSummary(
  concessionColumnNames.isActive,
  concessionItem => concessionItem.concession.isActive
);

const dateFromColumn = createWithoutSummary(
  concessionColumnNames.dateFrom,
  concessionItem => concessionItem.concession.dateFrom
);

const dateToColumn = createWithoutSummary(
  concessionColumnNames.dateTo,
  concessionItem => concessionItem.concession.dateTo
);

const deadlineColumn = createWithoutSummary(
  concessionColumnNames.deadline,
  concessionItem => concessionItem.benefit.deadline
);

const benefitTypeColumn = createWithoutSummary(
  concessionColumnNames.benefitType,
  concessionItem => concessionItem.benefit.type
);

const benefitKindColumn = createWithoutSummary(
  concessionColumnNames.benefitKind,
  concessionItem => concessionItem.benefit.kind
);

const amountTypeColumn = createWithoutSummary(
  concessionColumnNames.amountType,
  concessionItem => concessionItem.benefit.amountType
);

const amountValueColumn = createWithoutSummary(
  concessionColumnNames.amountValue,
  concessionItem => concessionItem.benefit.amountValue
);

const getPeriod = (concessionItem: ConcessionItem) => {
  const {benefit} = concessionItem;

  if (!benefit?.periodAmount || !benefit?.periodUnit) {
    return undefined;
  }

  return `${benefit.periodAmount} ${withConcessionBenefitPeriodUnit.getTitle(
    benefit.periodUnit
  )}`;
};

const periodColumn = createWithoutSummary(
  concessionColumnNames.period,
  getPeriod
);

const isRecurrentColumn = createWithoutSummary(
  concessionColumnNames.isRecurrent,
  concessionItem => concessionItem.benefit.isRecurrent
);

const freeMonthsAmountColumn = createWithoutSummary(
  concessionColumnNames.freeMonthsAmount,
  concessionItem => concessionItem.benefit.freeMonthsAmount
);

const freeMonthsUntilColumn = createWithoutSummary(
  concessionColumnNames.freeMonthsUntil,
  concessionItem => concessionItem.benefit.freeMonthsUntil
);

const oneTimeDollarsOffAmountColumn = createWithoutSummary(
  concessionColumnNames.oneTimeDollarsOffAmount,
  concessionItem => concessionItem.benefit.oneTimeDollarsOffAmount
);

const oneTimeDollarsOffPercentageColumn = createWithoutSummary(
  concessionColumnNames.oneTimeDollarsOffPercentage,
  concessionItem => concessionItem.benefit.oneTimeDollarsOffPercentage
);

const recurringDollarsOffAmountColumn = createWithoutSummary(
  concessionColumnNames.recurringDollarsOffAmount,
  concessionItem => concessionItem.benefit.recurringDollarsOffAmount
);

const recurringDollarsOffPercentageColumn = createWithoutSummary(
  concessionColumnNames.recurringDollarsOffPercentage,
  concessionItem => concessionItem.benefit.recurringDollarsOffPercentage
);

const recurringMonthsTermColumn = createWithoutSummary(
  concessionColumnNames.recurringMonthsTerm,
  concessionItem => concessionItem.benefit.recurringMonthsTerm
);

const leaseTermMonthsColumn = createWithoutSummary(
  concessionColumnNames.leaseTermMonths,
  concessionItem => concessionItem.benefit.leaseTermMonths
);

const conditionDeadlineColumn = createWithoutSummary(
  concessionColumnNames.conditionDeadline,
  concessionItem => concessionItem.benefit.conditionDeadline
);

const conditionBedroomsColumn = createWithoutSummary(
  concessionColumnNames.conditionBedrooms,
  concessionItem =>
    concessionItem.benefit.conditionBedrooms?.length !== 0
      ? concessionItem.benefit.conditionBedrooms
      : undefined
);

const conditionUnitNamesColumn = createWithoutSummary(
  concessionColumnNames.conditionUnitNames,
  concessionItem =>
    concessionItem.benefit.conditionUnitNames?.length !== 0
      ? concessionItem.benefit.conditionUnitNames
      : undefined
);

const conditionFloorplansColumn = createWithoutSummary(
  concessionColumnNames.conditionFloorplans,
  concessionItem =>
    concessionItem.benefit.conditionFloorplans?.length !== 0
      ? concessionItem.benefit.conditionFloorplans
      : undefined
);

const conditionSelectedUnitsColumn = createWithoutSummary(
  concessionColumnNames.conditionSelectedUnits,
  concessionItem => concessionItem.benefit.conditionSelectedUnits
);

const conditionSelectedFloorplansColumn = createWithoutSummary(
  concessionColumnNames.conditionSelectedFloorplans,
  concessionItem => concessionItem.benefit.conditionSelectedFloorplans
);

const conditionSelectedEmployeesColumn = createWithoutSummary(
  concessionColumnNames.conditionSelectedEmployees,
  concessionItem => concessionItem.benefit.conditionSelectedEmployees
);

const waivedApplicationFeeColumn = createWithoutSummary(
  concessionColumnNames.waivedApplicationFee,
  concessionItem => concessionItem.benefit.waivedApplicationFee
);

const waivedSecurityDepositColumn = createWithoutSummary(
  concessionColumnNames.waivedSecurityDeposit,
  concessionItem => concessionItem.benefit.waivedSecurityDeposit
);

const waivedAdministrativeFeeColumn = createWithoutSummary(
  concessionColumnNames.waivedAdministrativeFee,
  concessionItem => concessionItem.benefit.waivedAdministrativeFee
);

const waivedMoveInFeeColumn = createWithoutSummary(
  concessionColumnNames.waivedMoveInFee,
  concessionItem => concessionItem.benefit.waivedMoveInFee
);

const cheaperSecurityDepositColumn = createWithoutSummary(
  concessionColumnNames.cheaperSecurityDeposit,
  concessionItem => concessionItem.benefit.cheaperSecurityDeposit
);

const cheaperAdministrativeFeeColumn = createWithoutSummary(
  concessionColumnNames.cheaperAdministrativeFee,
  concessionItem => concessionItem.benefit.cheaperAdministrativeFee
);

const cheaperApplicationFeeColumn = createWithoutSummary(
  concessionColumnNames.cheaperApplicationFee,
  concessionItem => concessionItem.benefit.cheaperApplicationFee
);

const cheaperMoveInFeeColumn = createWithoutSummary(
  concessionColumnNames.cheaperMoveInFee,
  concessionItem => concessionItem.benefit.cheaperMoveInFee
);

const cheaperRentColumn = createWithoutSummary(
  concessionColumnNames.cheaperRent,
  concessionItem => concessionItem.benefit.cheaperRent
);

export {
  descriptionColumn,
  isActiveColumn,
  dateFromColumn,
  dateToColumn,
  deadlineColumn,
  propertyNameColumn,
  benefitTypeColumn,
  benefitKindColumn,
  amountTypeColumn,
  amountValueColumn,
  periodColumn,
  isRecurrentColumn,
  freeMonthsAmountColumn,
  freeMonthsUntilColumn,
  oneTimeDollarsOffAmountColumn,
  oneTimeDollarsOffPercentageColumn,
  recurringDollarsOffAmountColumn,
  recurringDollarsOffPercentageColumn,
  recurringMonthsTermColumn,
  leaseTermMonthsColumn,
  conditionDeadlineColumn,
  conditionBedroomsColumn,
  conditionUnitNamesColumn,
  conditionFloorplansColumn,
  conditionSelectedUnitsColumn,
  conditionSelectedFloorplansColumn,
  conditionSelectedEmployeesColumn,
  waivedApplicationFeeColumn,
  waivedSecurityDepositColumn,
  waivedAdministrativeFeeColumn,
  waivedMoveInFeeColumn,
  cheaperSecurityDepositColumn,
  cheaperAdministrativeFeeColumn,
  cheaperApplicationFeeColumn,
  cheaperMoveInFeeColumn,
  cheaperRentColumn,
  getTitle,
  getDescription
};

export type {ConcessionColumn};
