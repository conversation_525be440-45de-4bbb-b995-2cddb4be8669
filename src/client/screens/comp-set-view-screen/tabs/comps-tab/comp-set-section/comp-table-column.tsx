// TODO: Deprecate this one and use comp-table-column module now defined inside
//       rent insights tab, but that also needs to be moved along with a
//       reusable comp-table component to comp-set-view-screen components so we
//       could also use it here, in comps tab

import type {TableColumn} from "@unlockre/components-library/dist/virtual-table";
import {
  formatNumber,
  formatOptionalValue,
  formatPercentage
} from "@unlockre/utils-formatting/dist";

import {PercentagePill} from "@/client/components/percentage-pill";
import {PropertyQualityCell} from "@/client/components/property-quality";
import {PropertyImageCell} from "@/client/components/table-cells";
import {
  formatAvgDaysOnMarket,
  formatRatio,
  formatRent,
  formatRentPerSqft
} from "@/client/utils/formatting";
import {
  createNumberSortFunction,
  createStringSortFunction
} from "@/client/utils/table/sort-function";
import * as withComp from "@/domain/comp";
import * as withCompColumn from "@/domain/comp-column";
import {compSetReviewsProcessStatuses} from "@/domain/comp-set-reviews-process-status";
import type {CompSetReviewsProcessStatus} from "@/domain/comp-set-reviews-process-status";
import type {Comp} from "@/domain/domain-schema";
import {rentTypes} from "@/domain/rent-type";
import type {RentType} from "@/domain/rent-type";

import {PropertyInfoWithBadgesCell} from "./property-info-with-badges-cell";
import {ReviewSummaryCell} from "./review-summary-cell/review-summary-cell";

type CompTableColumn = TableColumn<Comp>;

type GetFirstColumnsParams = {
  compSetReviewsProcessStatus?: CompSetReviewsProcessStatus;
  comps: Comp[];
  onCompPick: (comp: Comp) => unknown;
  pickedComp?: Comp;
  showPropertyQuality?: boolean;
};

type GetAllParams = GetFirstColumnsParams & {
  rentType: RentType;
  showPropertyQuality?: boolean;
};

const {
  askingRentChangeCompColumn,
  avgAskingRentCompColumn,
  avgAskingRentPsfCompColumn,
  avgDaysOnMarketCompColumn,
  avgEffectiveRentCompColumn,
  avgEffectiveRentPsfCompColumn,
  concessionPercentageCompColumn,
  distanceCompColumn,
  effectiveRentChangeCompColumn,
  estimatedOccupancyCompColumn,
  exposureCompColumn,
  propertyQualityCompColumn,
  propertyReviewSummaryCompColumn,
  propertyStoriesCompColumn,
  similarityCompColumn,
  squareFeetCompColumn,
  unitsAvailableCompColumn,
  unitsCountCompColumn,
  yearBuiltCompColumn
} = withCompColumn;

const concessionPercentageCompTableColumn: CompTableColumn = {
  title: withCompColumn.getTitle(concessionPercentageCompColumn),
  key: concessionPercentageCompColumn.name,
  horizontalAlign: "end",
  width: 100,
  isSortable: true,
  description: withCompColumn.getDescription(concessionPercentageCompColumn),
  format: comp =>
    formatOptionalValue(
      concessionPercentageCompColumn.getValue(comp),
      formatPercentage
    ),
  getSummary: comps =>
    formatOptionalValue(
      concessionPercentageCompColumn.getSummaryValue(comps),
      formatPercentage
    ),
  sortFunction: createNumberSortFunction(
    comp => concessionPercentageCompColumn.getValue(comp) ?? 0
  )
};

const effectiveRentColumns: CompTableColumn[] = [
  {
    title: withCompColumn.getTitle(avgEffectiveRentCompColumn),
    key: avgEffectiveRentCompColumn.name,
    horizontalAlign: "end",
    width: 100,
    isSortable: true,
    description: withCompColumn.getDescription(avgEffectiveRentCompColumn),
    format: comp =>
      formatOptionalValue(
        avgEffectiveRentCompColumn.getValue(comp),
        formatRent
      ),
    getSummary: comps =>
      formatOptionalValue(
        avgEffectiveRentCompColumn.getSummaryValue(comps),
        formatRent
      ),
    sortFunction: createNumberSortFunction(
      comp => avgEffectiveRentCompColumn.getValue(comp) ?? 0
    )
  },
  {
    title: withCompColumn.getTitle(avgEffectiveRentPsfCompColumn),
    key: avgEffectiveRentPsfCompColumn.name,
    horizontalAlign: "end",
    width: 100,
    isSortable: true,
    description: withCompColumn.getDescription(avgEffectiveRentPsfCompColumn),
    format: comp =>
      formatOptionalValue(
        avgEffectiveRentPsfCompColumn.getValue(comp),
        formatRentPerSqft
      ),
    getSummary: comps =>
      formatOptionalValue(
        avgEffectiveRentPsfCompColumn.getSummaryValue(comps),
        formatRentPerSqft
      ),
    sortFunction: createNumberSortFunction(
      comp => avgEffectiveRentPsfCompColumn.getValue(comp) ?? 0
    )
  },
  concessionPercentageCompTableColumn,
  {
    title: withCompColumn.getTitle(effectiveRentChangeCompColumn),
    key: effectiveRentChangeCompColumn.name,
    horizontalAlign: "end",
    width: 100,
    isSortable: true,
    description: withCompColumn.getDescription(effectiveRentChangeCompColumn),
    format: comp =>
      formatOptionalValue(
        effectiveRentChangeCompColumn.getValue(comp),
        formatRatio
      ),
    sortFunction: createNumberSortFunction(
      comp => effectiveRentChangeCompColumn.getValue(comp) ?? 0
    )
  }
];

const askingRentColumns: CompTableColumn[] = [
  {
    title: withCompColumn.getTitle(avgAskingRentCompColumn),
    key: avgAskingRentCompColumn.name,
    horizontalAlign: "end",
    isSortable: true,
    description: withCompColumn.getDescription(avgAskingRentCompColumn),
    width: 100,
    format: comp =>
      formatOptionalValue(avgAskingRentCompColumn.getValue(comp), formatRent),
    getSummary: comps =>
      formatOptionalValue(
        avgAskingRentCompColumn.getSummaryValue(comps),
        formatRent
      ),
    sortFunction: createNumberSortFunction(
      comp => avgAskingRentCompColumn.getValue(comp) ?? 0
    )
  },
  {
    title: withCompColumn.getTitle(avgAskingRentPsfCompColumn),
    key: avgAskingRentPsfCompColumn.name,
    horizontalAlign: "end",
    isSortable: true,
    description: withCompColumn.getDescription(avgAskingRentPsfCompColumn),
    width: 100,
    format: comp =>
      formatOptionalValue(
        avgAskingRentPsfCompColumn.getValue(comp),
        formatRentPerSqft
      ),
    getSummary: comps =>
      formatOptionalValue(
        avgAskingRentPsfCompColumn.getSummaryValue(comps),
        formatRentPerSqft
      ),
    sortFunction: createNumberSortFunction(
      comp => avgAskingRentPsfCompColumn.getValue(comp) ?? 0
    )
  },
  concessionPercentageCompTableColumn,
  {
    title: withCompColumn.getTitle(askingRentChangeCompColumn),
    key: askingRentChangeCompColumn.name,
    horizontalAlign: "end",
    width: 100,
    isSortable: true,
    description: withCompColumn.getDescription(askingRentChangeCompColumn),
    format: comp =>
      formatOptionalValue(
        askingRentChangeCompColumn.getValue(comp),
        formatRatio
      ),
    sortFunction: createNumberSortFunction(
      comp => askingRentChangeCompColumn.getValue(comp) ?? 0
    )
  }
];

const propertyQualityColumn: CompTableColumn = {
  title: withCompColumn.getTitle(propertyQualityCompColumn),
  key: propertyQualityCompColumn.name,
  horizontalAlign: "start",
  isSortable: true,
  description: withCompColumn.getDescription(propertyQualityCompColumn),
  width: 100,
  sortFunction: createNumberSortFunction(
    comp => propertyQualityCompColumn.getValue(comp) ?? 0
  ),
  renderItemCell: ({item: comp}) => (
    <PropertyQualityCell
      qualitySummary={withComp.ensureProperty(comp).qualitySummary}
    />
  )
};

const areReviewsLoading = (
  comps: Comp[],
  compSetReviewsProcessStatus?: CompSetReviewsProcessStatus
) =>
  compSetReviewsProcessStatus === compSetReviewsProcessStatuses.running &&
  !comps.every(withComp.hasPropertyWithRating);

const getFirstColumns = ({
  comps,
  compSetReviewsProcessStatus,
  onCompPick,
  pickedComp,
  showPropertyQuality
}: GetFirstColumnsParams): CompTableColumn[] => [
  {
    title: "",
    key: "propertyImage",
    width: 134,
    isFixed: true,
    renderItemCell: ({item: comp, ...rest}) => (
      <PropertyImageCell
        compIndex={comps.indexOf(comp)}
        property={withComp.ensureProperty(comp)}
        {...rest}
      />
    ),
    getSummary: () => "Average"
  },
  {
    title: "Property",
    key: "property",
    horizontalAlign: "start",
    width: 320,
    isFixed: true,
    renderItemCell: ({item: comp, ...rest}) => (
      <PropertyInfoWithBadgesCell
        isPicked={pickedComp === comp}
        onNameClick={() => onCompPick(comp)}
        property={withComp.ensureProperty(comp)}
        withoutPadding
        {...rest}
      />
    )
  },
  {
    title: withCompColumn.getTitle(similarityCompColumn),
    key: similarityCompColumn.name,
    horizontalAlign: "start",
    isSortable: true,
    sortFunction: createNumberSortFunction(
      comp => similarityCompColumn.getValue(comp) ?? 0
    ),
    description: withCompColumn.getDescription(similarityCompColumn),
    width: 100,
    renderTableCell: comp => (
      <PercentagePill score={similarityCompColumn.getValue(comp)} />
    )
  },
  ...(showPropertyQuality ? [propertyQualityColumn] : []),
  {
    title: withCompColumn.getTitle(propertyReviewSummaryCompColumn),
    key: propertyReviewSummaryCompColumn.name,
    horizontalAlign: "center",
    isSortable: true,
    width: 100,
    sortFunction: createNumberSortFunction(
      comp => propertyReviewSummaryCompColumn.getValue(comp)?.rating ?? 0
    ),
    description: withCompColumn.getDescription(propertyReviewSummaryCompColumn),
    renderItemCell: ({item: comp, ...rest}) => (
      <ReviewSummaryCell
        {...rest}
        areReviewsLoading={areReviewsLoading(
          [comp],
          compSetReviewsProcessStatus
        )}
        reviewSummary={propertyReviewSummaryCompColumn.getValue(comp)}
      />
    ),
    renderItemsSummaryCell: ({items: comps, ...rest}) => (
      <ReviewSummaryCell
        {...rest}
        areReviewsLoading={areReviewsLoading(
          comps,
          compSetReviewsProcessStatus
        )}
        reviewSummary={propertyReviewSummaryCompColumn.getSummaryValue(comps)}
      />
    )
  },
  {
    title: withCompColumn.getTitle(distanceCompColumn),
    key: distanceCompColumn.name,
    horizontalAlign: "end",
    isSortable: true,
    width: 100,
    description: withCompColumn.getDescription(distanceCompColumn),
    format: comp => formatNumber(distanceCompColumn.getValue(comp)),
    getSummary: comps =>
      formatOptionalValue(
        distanceCompColumn.getSummaryValue(comps),
        formatNumber
      )
  },
  {
    title: withCompColumn.getTitle(yearBuiltCompColumn),
    key: yearBuiltCompColumn.name,
    horizontalAlign: "end",
    width: 100,
    description: withCompColumn.getDescription(yearBuiltCompColumn),
    isSortable: true,
    format: comp => formatOptionalValue(yearBuiltCompColumn.getValue(comp)),
    getSummary: comps =>
      formatOptionalValue(yearBuiltCompColumn.getSummaryValue(comps)),
    sortFunction: createStringSortFunction(
      comp => formatOptionalValue(yearBuiltCompColumn.getValue(comp)) ?? ""
    )
  },
  {
    title: withCompColumn.getTitle(propertyStoriesCompColumn),
    key: propertyStoriesCompColumn.name,
    horizontalAlign: "end",
    width: 100,
    isSortable: true,
    description: withCompColumn.getDescription(propertyStoriesCompColumn),
    format: comp =>
      formatOptionalValue(propertyStoriesCompColumn.getValue(comp)),
    getSummary: comps =>
      formatOptionalValue(
        propertyStoriesCompColumn.getSummaryValue(comps),
        number => formatNumber(number, {maximumFractionDigits: 0})
      ),
    sortFunction: createNumberSortFunction(
      comp => propertyStoriesCompColumn.getValue(comp) ?? 0
    )
  },
  {
    title: withCompColumn.getTitle(unitsCountCompColumn),
    key: unitsCountCompColumn.name,
    horizontalAlign: "end",
    width: 100,
    description: withCompColumn.getDescription(unitsCountCompColumn),
    isSortable: true,
    format: comp =>
      formatOptionalValue(unitsCountCompColumn.getValue(comp), formatNumber),
    getSummary: comps =>
      formatOptionalValue(
        unitsCountCompColumn.getSummaryValue(comps),
        unitsTotalSummary =>
          formatNumber(unitsTotalSummary, {maximumFractionDigits: 0})
      ),
    sortFunction: createNumberSortFunction(
      comp => unitsCountCompColumn.getValue(comp) ?? 0
    )
  },
  {
    title: withCompColumn.getTitle(unitsAvailableCompColumn),
    key: unitsAvailableCompColumn.name,
    horizontalAlign: "end",
    width: 100,
    description: withCompColumn.getDescription(unitsAvailableCompColumn),
    isSortable: true,
    format: comp =>
      formatOptionalValue(unitsAvailableCompColumn.getValue(comp)),
    getSummary: comps =>
      formatOptionalValue(
        unitsAvailableCompColumn.getSummaryValue(comps),
        unitsAvailableSummary =>
          formatNumber(unitsAvailableSummary, {maximumFractionDigits: 0})
      ),
    sortFunction: createNumberSortFunction(
      comp => unitsAvailableCompColumn.getValue(comp) ?? 0
    )
  },
  {
    key: estimatedOccupancyCompColumn.name,
    title: withCompColumn.getTitle(estimatedOccupancyCompColumn),
    description: withCompColumn.getDescription(estimatedOccupancyCompColumn),
    width: 100,
    horizontalAlign: "end",
    isSortable: true,
    format: comp =>
      formatOptionalValue(
        estimatedOccupancyCompColumn.getValue(comp),
        formatRatio
      ),
    sortFunction: createNumberSortFunction(
      comp => estimatedOccupancyCompColumn.getValue(comp) ?? 0
    )
  },
  {
    title: withCompColumn.getTitle(exposureCompColumn),
    key: exposureCompColumn.name,
    horizontalAlign: "end",
    width: 100,
    isSortable: true,
    description: withCompColumn.getDescription(exposureCompColumn),
    format: comp =>
      formatOptionalValue(exposureCompColumn.getValue(comp), formatRatio),
    sortFunction: createNumberSortFunction(
      comp => exposureCompColumn.getValue(comp) ?? 0
    )
  },
  {
    title: withCompColumn.getTitle(avgDaysOnMarketCompColumn),
    key: avgDaysOnMarketCompColumn.name,
    horizontalAlign: "end",
    width: 100,
    description: withCompColumn.getDescription(avgDaysOnMarketCompColumn),
    isSortable: true,
    format: comp =>
      formatOptionalValue(
        avgDaysOnMarketCompColumn.getValue(comp),
        formatAvgDaysOnMarket
      ),
    getSummary: comps =>
      formatOptionalValue(
        avgDaysOnMarketCompColumn.getSummaryValue(comps),
        formatAvgDaysOnMarket
      ),
    sortFunction: createNumberSortFunction(
      comp => avgDaysOnMarketCompColumn.getValue(comp) ?? 0
    )
  },
  {
    title: withCompColumn.getTitle(squareFeetCompColumn),
    key: squareFeetCompColumn.name,
    horizontalAlign: "end",
    description: withCompColumn.getDescription(squareFeetCompColumn),
    width: 100,
    isSortable: true,
    format: comp =>
      formatOptionalValue(squareFeetCompColumn.getValue(comp), formatNumber),
    getSummary: comps =>
      formatOptionalValue(
        squareFeetCompColumn.getSummaryValue(comps),
        averageSqft => formatNumber(averageSqft, {maximumFractionDigits: 0})
      ),
    sortFunction: createNumberSortFunction(
      comp => squareFeetCompColumn.getValue(comp) ?? 0
    )
  }
];

const getAll = ({rentType, ...rest}: GetAllParams) => [
  ...getFirstColumns(rest),
  ...(rentType === rentTypes.asking ? askingRentColumns : effectiveRentColumns)
];

export {getAll};
