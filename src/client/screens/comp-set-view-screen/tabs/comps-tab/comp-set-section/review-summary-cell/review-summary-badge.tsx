import {Star} from "@phosphor-icons/react";
import type {ComponentProps} from "react";
import styled from "styled-components";

import {CategoryBadge} from "@/client/components/badges";
import {formatRating} from "@/client/utils/formatting";
import type {ReviewSummary} from "@/domain/domain-schema";

import {ReviewSummaryBadgeTooltip} from "./review-summary-badge-tooltip";
import {usePersistentTooltip} from "./use-persistent-tooltip";

type Props = {
  areReviewsLoading?: boolean;
  reviewSummary?: ReviewSummary;
};

type CategoryBadgeProps = ComponentProps<typeof CategoryBadge>;

type ColorProps = Pick<CategoryBadgeProps, "colorCategory" | "colorShade">;

const getColorProps = (rating: number): ColorProps =>
  rating <= 3
    ? {colorCategory: "orange", colorShade: 2}
    : {colorCategory: "lavender", colorShade: 3};

const StyledCategoryBadge = styled(CategoryBadge)`
  min-width: 50px;
`;

const ReviewSummaryBadge = ({areReviewsLoading, reviewSummary}: Props) => {
  const {isTooltipOpened, referenceProps, tooltipProps} =
    usePersistentTooltip();

  return (
    <>
      <StyledCategoryBadge
        {...referenceProps}
        {...getColorProps(reviewSummary?.rating ?? 0)}
        isLoading={areReviewsLoading}
        leftIcon={Star}
        value={
          reviewSummary?.rating === undefined
            ? null
            : formatRating(reviewSummary.rating)
        }
      />
      {isTooltipOpened && (
        <ReviewSummaryBadgeTooltip
          {...{areReviewsLoading, reviewSummary}}
          {...tooltipProps}
        />
      )}
    </>
  );
};

export {ReviewSummaryBadge};
