import {ItemCell} from "@unlockre/components-library/dist/virtual-table";
import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ComponentProps} from "react";

import {ReviewSummaryBadge} from "./review-summary-badge";

type ReviewSummaryBadgeProps = ComponentProps<typeof ReviewSummaryBadge>;

type ItemCellProps<TItem extends AnyObject> = ComponentProps<
  typeof ItemCell<TItem>
>;

type ExposedItemCellProps<TItem extends AnyObject> = Omit<
  ItemCellProps<TItem>,
  "children" | "item"
>;

// prettier-ignore
type Props<TItem extends AnyObject> =
  & ExposedItemCellProps<TItem>
  & ReviewSummaryBadgeProps;

const ReviewSummaryCell = <TItem extends AnyObject>({
  areReviewsLoading,
  reviewSummary,
  ...rest
}: Props<TItem>) => (
  <ItemCell {...rest}>
    <ReviewSummaryBadge {...{areReviewsLoading, reviewSummary}} />
  </ItemCell>
);

export {ReviewSummaryCell};
