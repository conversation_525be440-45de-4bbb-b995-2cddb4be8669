import {useMemo} from "react";

import {trpc} from "@/client/utils/trpc";
import * as withComp from "@/domain/comp";
import * as withCompSet from "@/domain/comp-set";
import * as withProperty from "@/domain/property";

type Params = {
  compSetId: string;
  dateFrom: string;
  dateTo: string;
};

const useCompSetListings = ({compSetId, dateFrom, dateTo}: Params) => {
  const listingsRelation = {
    name: "property.listings",
    params: {dateFrom, dateTo}
  } as const;

  const compSetResponse = trpc.getCompSet.useQuery({
    compSetId,
    relations: [{name: "property"}, listingsRelation]
  });

  const compSet = compSetResponse?.data;

  const compSetCompsResponse = trpc.getCompSetComps.useQuery({
    compSetId,
    relations: [{name: "property"}, listingsRelation]
  });

  const compSetComps = compSetCompsResponse.data;

  const areListingsLoading =
    compSetResponse.isLoading || compSetCompsResponse.isLoading;

  const listings = useMemo(
    () =>
      compSet && compSetComps
        ? [
            ...withProperty.ensureListings(withCompSet.ensureProperty(compSet)),
            ...compSetComps.flatMap(comp =>
              withProperty.ensureListings(withComp.ensureProperty(comp))
            )
          ]
        : [],
    [compSet, compSetComps]
  );

  return {listings, areListingsLoading};
};

export {useCompSetListings};
