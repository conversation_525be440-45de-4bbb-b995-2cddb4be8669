import {useIsPrintView} from "@unlockre/utils-react/dist";
import {useMemo} from "react";

import type {Property} from "@/domain/domain-schema";

import * as withListingItem from "./listing-item";

const useListingItems = (
  allProperties: Property[],
  compSetProperty?: Property
) => {
  const isPrintView = useIsPrintView();

  const listingItems = useMemo(() => {
    if (!isPrintView) {
      return allProperties.flatMap(withListingItem.createAllFrom);
    }

    return compSetProperty
      ? withListingItem.createAllFrom(compSetProperty)
      : [];
  }, [allProperties, compSetProperty, isPrintView]);

  return listingItems;
};

export {useListingItems};
