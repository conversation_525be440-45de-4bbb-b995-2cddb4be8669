import {useIsPrintView} from "@unlockre/utils-react/dist";

import {useUserSplitIoTreatments} from "@/client/utils/split-io";
import type {FeatureFlagName} from "@/client/utils/split-io/feature-flag-name";

import {useRentInsightsTabSelection} from "./use-rent-insights-tab-selection";

const featureFlagNames = [
  "showPricingRecommendations",
  "showByFloorPlanTab"
] as const satisfies readonly FeatureFlagName[];

const useRentInsightsTabView = () => {
  const tabSelection = useRentInsightsTabSelection();

  const isPrintView = useIsPrintView();

  const treatments = useUserSplitIoTreatments(featureFlagNames);

  const showPricingRecommendations =
    treatments?.showPricingRecommendations.treatment === "on";

  const showByFloorPlanTab = treatments?.showByFloorPlanTab.treatment === "on";

  return {
    isPrintView,
    showByFloorPlanTab,
    showPricingRecommendations,
    tabSelection
  };
};

export {useRentInsightsTabView};
