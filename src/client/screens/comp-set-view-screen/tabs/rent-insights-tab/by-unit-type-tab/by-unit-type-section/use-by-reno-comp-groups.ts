import {useUrlQuery} from "@unlockre/utils-next/dist";
import {useMemo} from "react";

import * as withUrlQuery from "@/client/screens/comp-set-view-screen/url-query";
import {trpc, useAllCompSetComps} from "@/client/utils/trpc";
import type {Property} from "@/domain/domain-schema";
import {renovationStatuses} from "@/domain/renovation-status";
import type {KnownRenovationStatus} from "@/domain/renovation-status";
import {unitGroupings} from "@/domain/unit-grouping";
import type {NonByPropertyUnitGrouping} from "@/domain/unit-grouping";

import * as withByRenoCompGroup from "./by-reno-comp-group";
import type {ByRenoCompGroupUnitRentSummary} from "./by-reno-comp-group";

type Params<TUnitGrouping extends NonByPropertyUnitGrouping> = {
  unitGrouping: TUnitGrouping;
  withRenovationInfo: boolean;
};

type UseHistoricalRentSummariesParams = {
  isEnabled: boolean;
  propertyIds?: string[];
  unitRenovationStatus?: KnownRenovationStatus;
};

type UseUnitRentSummariesParams<
  TUnitGrouping extends NonByPropertyUnitGrouping
> = {
  isEnabled: boolean;
  propertyIds?: string[];
  unitGrouping: TUnitGrouping;
  unitRenovationStatus?: KnownRenovationStatus;
};

// eslint-disable-next-line complexity
const useUnitRentSummaries = <TUnitGrouping extends NonByPropertyUnitGrouping>({
  isEnabled,
  propertyIds,
  unitGrouping,
  unitRenovationStatus
}: UseUnitRentSummariesParams<TUnitGrouping>) => {
  const {dateFrom, dateTo} = useUrlQuery(withUrlQuery.getFrom).urlQuery;

  const unitRentSummariesByBedroomResponse = trpc.getUnitRentSummaries.useQuery(
    {
      dateFrom,
      dateTo,
      propertyIds: propertyIds ?? [],
      unitGrouping,
      unitRenovationStatus
    },
    {enabled: propertyIds !== undefined && isEnabled}
  );

  const unitRentSummariesByPropertyResponse =
    trpc.getUnitRentSummaries.useQuery(
      {
        dateFrom,
        dateTo,
        propertyIds: propertyIds ?? [],
        unitGrouping: unitGroupings.byProperty,
        unitRenovationStatus
      },
      {enabled: propertyIds !== undefined && isEnabled}
    );

  const unitRentSummaries = [
    ...(unitRentSummariesByBedroomResponse.data ?? []),
    ...(unitRentSummariesByPropertyResponse.data ?? [])
  ] as unknown as ByRenoCompGroupUnitRentSummary<TUnitGrouping>[];

  const areUnitRentSummariesLoading =
    unitRentSummariesByBedroomResponse.isLoading ||
    unitRentSummariesByPropertyResponse.isLoading;

  return [unitRentSummaries, areUnitRentSummariesLoading] as const;
};

const useHistoricalRentSummaries = ({
  isEnabled,
  propertyIds,
  unitRenovationStatus
}: UseHistoricalRentSummariesParams) => {
  const {dateFrom, dateTo} = useUrlQuery(withUrlQuery.getFrom).urlQuery;

  const historicalRentSummariesResponse =
    trpc.getHistoricalRentSummaries.useQuery(
      {
        dateFrom,
        dateTo,
        propertyIds: propertyIds ?? [],
        unitRenovationStatus
      },
      {enabled: propertyIds !== undefined && isEnabled}
    );

  const historicalRentSummaries = historicalRentSummariesResponse.data;

  const areHistoricalRentSummariesLoading =
    historicalRentSummariesResponse.isLoading;

  return [historicalRentSummaries, areHistoricalRentSummariesLoading] as const;
};

const useComps = (compSetProperty?: Property) => {
  const {compSetId, dateFrom, dateTo} = useUrlQuery(
    withUrlQuery.getFrom
  ).urlQuery;

  const {areCompSetCompsLoading, compSetComps} = useAllCompSetComps({
    compRelations: [
      {name: "property"},
      {
        name: "property.historicalRentSummary",
        params: {
          dateFrom,
          dateTo
        }
      }
    ],
    compSetId,
    compSetProperty
  });

  const compPropertyIds = useMemo(
    () => compSetComps?.map(comp => comp.propertyId),
    [compSetComps]
  );

  return {
    areCompsLoading: areCompSetCompsLoading,
    comps: compSetComps,
    compPropertyIds
  };
};

const useCompSet = () => {
  const {compSetId, dateFrom, dateTo} = useUrlQuery(
    withUrlQuery.getFrom
  ).urlQuery;

  const compSetResponse = trpc.getCompSet.useQuery({
    compSetId,
    relations: [
      {name: "property"},
      {
        name: "property.historicalRentSummary",
        params: {
          dateFrom,
          dateTo
        }
      }
    ]
  });

  const compSet = compSetResponse.data;

  const isCompSetLoading = compSetResponse.isLoading;

  return {
    compSet,
    isCompSetLoading
  };
};

// eslint-disable-next-line complexity, max-statements
const useByRenoCompGroups = <TUnitGrouping extends NonByPropertyUnitGrouping>({
  unitGrouping,
  withRenovationInfo
}: Params<TUnitGrouping>) => {
  const {compSet, isCompSetLoading} = useCompSet();

  const {areCompsLoading, compPropertyIds, comps} = useComps(compSet?.property);

  const [allUnitRentSummaries, areAllUnitRentSummariesLoading] =
    useUnitRentSummaries({
      isEnabled: true,
      unitGrouping,
      propertyIds: compPropertyIds
    });

  const [nonRenoUnitRentSummaries, areNonRenoUnitRentSummariesLoading] =
    useUnitRentSummaries({
      isEnabled: withRenovationInfo,
      unitGrouping,
      propertyIds: compPropertyIds,
      unitRenovationStatus: renovationStatuses.nonRenovated
    });

  const [renoUnitRentSummaries, areRenoUnitRentSummariesLoading] =
    useUnitRentSummaries({
      isEnabled: withRenovationInfo,
      unitGrouping,
      propertyIds: compPropertyIds,
      unitRenovationStatus: renovationStatuses.renovated
    });

  const [allHistoricalRentSummaries, areAllHistoricalRentSummariesLoading] =
    useHistoricalRentSummaries({
      isEnabled: true,
      propertyIds: compPropertyIds
    });

  const [
    nonRenoHistoricalRentSummaries,
    areNonRenoHistoricalRentSummariesLoading
  ] = useHistoricalRentSummaries({
    isEnabled: withRenovationInfo,
    propertyIds: compPropertyIds,
    unitRenovationStatus: renovationStatuses.nonRenovated
  });

  const [renoHistoricalRentSummaries, areRenoHistoricalRentSummariesLoading] =
    useHistoricalRentSummaries({
      isEnabled: withRenovationInfo,
      propertyIds: compPropertyIds,
      unitRenovationStatus: renovationStatuses.renovated
    });

  const byRenoCompGroups = useMemo(
    // eslint-disable-next-line complexity
    () =>
      comps &&
      withByRenoCompGroup.createAllFrom({
        allHistoricalRentSummaries: allHistoricalRentSummaries ?? [],
        allUnitRentSummaries: allUnitRentSummaries ?? [],
        comps,
        nonRenoHistoricalRentSummaries: nonRenoHistoricalRentSummaries ?? [],
        nonRenoUnitRentSummaries: nonRenoUnitRentSummaries ?? [],
        renoHistoricalRentSummaries: renoHistoricalRentSummaries ?? [],
        renoUnitRentSummaries: renoUnitRentSummaries ?? []
      }),
    [
      comps,
      allHistoricalRentSummaries,
      nonRenoHistoricalRentSummaries,
      renoHistoricalRentSummaries,
      allUnitRentSummaries,
      nonRenoUnitRentSummaries,
      renoUnitRentSummaries
    ]
  );

  const areByRenoCompGroupsLoading =
    isCompSetLoading ||
    areCompsLoading ||
    areAllHistoricalRentSummariesLoading ||
    areAllUnitRentSummariesLoading ||
    (withRenovationInfo && areNonRenoHistoricalRentSummariesLoading) ||
    (withRenovationInfo && areRenoHistoricalRentSummariesLoading) ||
    (withRenovationInfo && areNonRenoUnitRentSummariesLoading) ||
    (withRenovationInfo && areRenoUnitRentSummariesLoading);

  return {
    areByRenoCompGroupsLoading,
    byRenoCompGroups
  };
};

export {useByRenoCompGroups};
