import {useUrlQuery} from "@unlockre/utils-next/dist";
import {useState} from "react";

import {useTrackCompSetViewEvent} from "@/client/screens/comp-set-view-screen/comp-set-view-event";
import * as withUrlQuery from "@/client/screens/comp-set-view-screen/url-query";
import {useUserSplitIoTreatments} from "@/client/utils/split-io";
import type {FeatureFlagName} from "@/client/utils/split-io/feature-flag-name";
import type {Property, UnitConfig} from "@/domain/domain-schema";
import type {KnownListingStatus} from "@/domain/listing-status";
import type {KnownRenovationStatus} from "@/domain/renovation-status";
import {rentTypes} from "@/domain/rent-type";
import type {RentType} from "@/domain/rent-type";
import {unitGroupings} from "@/domain/unit-grouping";
import {unitRentGroupings} from "@/domain/unit-rent-grouping";
import type {UnitRentGrouping} from "@/domain/unit-rent-grouping";

import {
  getListing<PERSON>tatus<PERSON>ilter,
  getPropertyFilter,
  getRenovationStatusFilter,
  getUnitConfigFilter
} from "../../../filters";
import {usePropertySelectField} from "../../../use-property-select-field";
import type {MixedFiltersArray} from "../../filter-pop-over/filter";
import {useFilterPopOver} from "../../filter-pop-over/use-filter-pop-over";

import {useFilteredByUnitItem} from "./use-filtered-by-unit-item";
import {useUnitConfigs} from "./use-unit-configs";
import {useUnitItems} from "./use-unit-items";

const featureFlagNames = [
  "showRenovationInfo"
] as const satisfies readonly FeatureFlagName[];

// eslint-disable-next-line max-statements
const useUnitsSection = () => {
  const {dateFrom, dateTo} = useUrlQuery(withUrlQuery.getFrom).urlQuery;
  const [rentType, setRentType] = useState<RentType>(rentTypes.asking);

  const treatments = useUserSplitIoTreatments(featureFlagNames);

  const showRenovationInfo = treatments?.showRenovationInfo.treatment === "on";

  const [unitRentGrouping, setUnitRentGrouping] = useState<UnitRentGrouping>(
    unitRentGroupings.byUnit
  );

  const [unitConfigs, setUnitConfigs] = useState<UnitConfig[]>([]);

  const [unitRenovationStatus, setUnitRenovationStatus] =
    useState<KnownRenovationStatus | null>(null);

  const [selectedListingStatus, setSelectedListingStatus] =
    useState<KnownListingStatus | null>(null);

  const {
    arePropertiesLoading,
    handlePropertyChange: onPropertyChange,
    properties,
    selectedProperties,
    selectProperties
  } = usePropertySelectField([
    {
      name: "property.unitRentSummary",
      params: {
        dateFrom,
        dateTo,
        unitGrouping: unitGroupings.byUnit
      }
    }
  ]);

  const allUnitItems = useUnitItems(properties);

  const allUnitConfigs = useUnitConfigs(allUnitItems);

  const filteredUnitItems = useFilteredByUnitItem({
    unitItems: allUnitItems,
    unitConfigs,
    unitRenovationStatus,
    propertyIds: selectedProperties.map(property => property.id)
  });

  const {
    filterPopOverProps,
    isFilterPopOverOpen,
    reference,
    toggleFilterPopOver
  } = useFilterPopOver<HTMLDivElement>();

  const isLoading = arePropertiesLoading;

  const trackEvent = useTrackCompSetViewEvent();

  const handleRentTypeChange = (newRentType: RentType) => {
    setRentType(newRentType);

    trackEvent({
      type: "compSetView/rentInsightsByUnitTable/changeRentType",
      data: {rentType: newRentType}
    });
  };

  const handleUnitRentGroupingChange = (
    newUnitRentGrouping: UnitRentGrouping
  ) => {
    setUnitRentGrouping(newUnitRentGrouping);

    trackEvent({
      type: "compSetView/rentInsightsByUnitTable/changeUnitRentGrouping",
      data: {unitRentGrouping: newUnitRentGrouping}
    });
  };

  const handleUnitRenovationStatusChange = (
    newUnitRenovationStatus: KnownRenovationStatus | null
  ) => {
    setUnitRenovationStatus(newUnitRenovationStatus);

    trackEvent({
      type: "compSetView/rentInsightsByUnitTable/changeUnitRenovationStatus",
      data: {unitRenovationStatus: newUnitRenovationStatus}
    });
  };

  const handleUnitConfigsChange = (newUnitConfigs: UnitConfig[]) => {
    setUnitConfigs(newUnitConfigs);

    trackEvent({
      type: "compSetView/rentInsightsByUnitTable/changeUnitConfigs",
      data: {unitConfigs: newUnitConfigs}
    });
  };

  const handlePropertyChange = (properties: Property[]) =>
    onPropertyChange(
      {
        type: "compSetView/rentInsightsTab/byUnit/changeProperty",
        data: {propertyIds: properties.map(property => property.id)}
      },
      properties
    );

  const handleListingStatusChange = (
    listingStatus: KnownListingStatus | null
  ) => setSelectedListingStatus(listingStatus);

  const filters: MixedFiltersArray = [
    getPropertyFilter({
      handlePropertyChange,
      properties,
      selectedProperties
    }),
    getUnitConfigFilter({
      allUnitConfigs,
      handleUnitConfigsChange,
      unitConfigs
    }),
    getRenovationStatusFilter({
      handleUnitRenovationStatusChange,
      unitRenovationStatus
    }),
    getListingStatusFilter({
      handleListingStatusChange,
      selectedListingStatus
    })
  ];

  return {
    allUnitConfigs,
    filters,
    handlePropertyChange,
    handleRentTypeChange,
    handleUnitConfigsChange,
    handleUnitRenovationStatusChange,
    handleUnitRentGroupingChange,
    isLoading,
    properties,
    rentType,
    selectedProperties,
    showRenovationInfo,
    unitConfigs,
    filteredUnitItems,
    unitRenovationStatus,
    unitRentGrouping,
    selectProperties,
    filterPopOverProps,
    toggleFilterPopOver,
    isFilterPopOverOpen,
    reference
  };
};

export {useUnitsSection};
