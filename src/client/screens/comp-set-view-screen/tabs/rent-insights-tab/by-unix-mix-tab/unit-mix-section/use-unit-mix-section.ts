import {useUrlQuery} from "@unlockre/utils-next/dist";
import {useMemo, useState} from "react";

import {useTrackCompSetViewEvent} from "@/client/screens/comp-set-view-screen/comp-set-view-event";
import * as withUrlQuery from "@/client/screens/comp-set-view-screen/url-query";
import {useUserSplitIoTreatments} from "@/client/utils/split-io";
import type {FeatureFlagName} from "@/client/utils/split-io/feature-flag-name";
import {trpc, useAllCompSetComps} from "@/client/utils/trpc";
import * as withComp from "@/domain/comp";
import type {ByUnitMixUnitRentSummary, Property} from "@/domain/domain-schema";
import {renovationStatuses} from "@/domain/renovation-status";
import type {KnownRenovationStatus} from "@/domain/renovation-status";
import {rentTypes} from "@/domain/rent-type";
import type {RentType} from "@/domain/rent-type";
import {unitGroupings} from "@/domain/unit-grouping";
import {unitRentGroupings} from "@/domain/unit-rent-grouping";
import type {UnitRentGrouping} from "@/domain/unit-rent-grouping";
import type {UnitTypeBedroomQuantity} from "@/domain/unit-type";
import {useBoolean} from "@/utils/react";

import {getPropertyFilter} from "../../../filters";
import {usePropertySelectField} from "../../../use-property-select-field";
import type {MixedFiltersArray} from "../../filter-pop-over/filter";
import {useFilterPopOver} from "../../filter-pop-over/use-filter-pop-over";

import * as withUnitMixItem from "./unit-mix-item";
import {useFilteredUnitMixItems} from "./use-filtered-unit-mix-items";

const featureFlagNames = [
  "showUnitMixConfidenceColumn",
  "showRenovationInfo"
] as const satisfies readonly FeatureFlagName[];

type UnitRentSummariesParams = {
  isEnabled: boolean;
  propertyIds?: string[];
  unitRenovationStatus?: KnownRenovationStatus;
};

// TODO: If necessary, move this hook to a parent directory and reuse in use-multi-comps.ts.
const useUnitRentSummaries = ({
  isEnabled,
  propertyIds,
  unitRenovationStatus
}: UnitRentSummariesParams) => {
  const {dateFrom, dateTo} = useUrlQuery(withUrlQuery.getFrom).urlQuery;

  const unitRentSummariesResponse = trpc.getUnitRentSummaries.useQuery(
    {
      dateFrom,
      dateTo,
      propertyIds: propertyIds ?? [],
      unitGrouping: unitGroupings.byUnitMix,
      unitRenovationStatus
    },
    {enabled: propertyIds !== undefined && isEnabled}
  );

  const unitRentSummaries = unitRentSummariesResponse.data as
    | ByUnitMixUnitRentSummary[]
    | undefined;

  const areUnitRentSummariesLoading = unitRentSummariesResponse.isLoading;

  return [unitRentSummaries, areUnitRentSummariesLoading] as const;
};

// eslint-disable-next-line max-statements, complexity
const useUnitMixSection = () => {
  const [rentType, setRentType] = useState<RentType>(rentTypes.asking);

  const [showRenovationInfo, toggleShowRenovationInfo] = useBoolean(false);

  const [unitRentGrouping, setUnitRentGrouping] = useState<UnitRentGrouping>(
    unitRentGroupings.byUnit
  );

  const {compSetId, dateFrom, dateTo} = useUrlQuery(
    withUrlQuery.getFrom
  ).urlQuery;

  const treatments = useUserSplitIoTreatments(featureFlagNames);

  const showUnitMixConfidenceColumn =
    treatments?.showUnitMixConfidenceColumn.treatment === "on";

  const canShowRenovationInfo =
    treatments?.showRenovationInfo.treatment === "on";

  const trackEvent = useTrackCompSetViewEvent();

  const {
    arePropertiesLoading,
    handlePropertyChange: onPropertyChange,
    properties,
    selectedProperties,
    selectProperties
  } = usePropertySelectField();

  const compSetResponse = trpc.getCompSet.useQuery({
    compSetId,
    relations: [
      {name: "property"},
      {
        name: "property.unitRentSummary",
        params: {
          dateFrom,
          dateTo,
          unitGrouping: unitGroupings.byUnitMix
        }
      }
    ]
  });

  const compSet = compSetResponse.data;

  const isCompSetLoading = compSetResponse.isLoading;

  const {areCompSetCompsLoading, compSetComps} = useAllCompSetComps({
    compRelations: [
      {name: "property"},
      {
        name: "property.unitRentSummary",
        params: {
          dateFrom,
          dateTo,
          unitGrouping: unitGroupings.byUnitMix
        }
      }
    ],
    compSetId,
    compSetProperty: compSet?.property
  });

  const propertyIds =
    compSet?.property && compSetComps
      ? [
          compSet.property.id,
          ...compSetComps.map(comp => withComp.ensureProperty(comp).id)
        ]
      : undefined;

  // TODO: Check if we could get this data in another way.
  const [
    nonRenovatedUnitRentSummaries,
    areNonRenovatedUnitRentSummariesLoading
  ] = useUnitRentSummaries({
    isEnabled: compSetComps !== undefined,
    propertyIds,
    unitRenovationStatus: renovationStatuses.nonRenovated
  });

  const [renovatedUnitRentSummaries, areRenovatedUnitRentSummariesLoading] =
    useUnitRentSummaries({
      isEnabled: compSetComps !== undefined,
      propertyIds,
      unitRenovationStatus: renovationStatuses.renovated
    });

  const {
    filterPopOverProps,
    isFilterPopOverOpen,
    reference,
    toggleFilterPopOver
  } = useFilterPopOver<HTMLDivElement>();

  const isLoading =
    isCompSetLoading ||
    areCompSetCompsLoading ||
    !treatments ||
    areNonRenovatedUnitRentSummariesLoading ||
    areRenovatedUnitRentSummariesLoading;

  const allUnitMixItems = useMemo(
    () =>
      compSetComps &&
      withUnitMixItem
        .createAllFrom({
          properties: compSetComps.map(withComp.ensureProperty),
          nonRenovatedUnitRentSummaries,
          renovatedUnitRentSummaries
        })
        .sort(withUnitMixItem.compare),
    [compSetComps, nonRenovatedUnitRentSummaries, renovatedUnitRentSummaries]
  );

  const filteredUnitMixItems = useFilteredUnitMixItems({
    allUnitMixItems,
    propertyIds: selectedProperties.map(property => property.id)
  });

  const bedroomQuantities = useMemo(() => {
    if (!allUnitMixItems?.length) {
      return [];
    }

    const maxBedrooms = Math.max(
      ...allUnitMixItems
        .flatMap(unitMixItem => unitMixItem.property.unitMixes)
        .map(unitMix => unitMix.bedrooms)
    );

    // TODO: Is it possible to avoid casting?
    return [...Array(maxBedrooms + 1).keys()] as UnitTypeBedroomQuantity[];
  }, [allUnitMixItems]);

  const handleRentTypeChange = (newRentType: RentType) => {
    setRentType(newRentType);

    trackEvent({
      type: "compSetView/rentInsightsByUnitMixTable/changeRentType",
      data: {rentType: newRentType}
    });
  };

  const handleUnitRentGroupingChange = (
    newUnitRentGrouping: UnitRentGrouping
  ) => {
    setUnitRentGrouping(newUnitRentGrouping);

    trackEvent({
      type: "compSetView/rentInsightsByUnitMixTable/changeUnitRentGrouping",
      data: {unitRentGrouping: newUnitRentGrouping}
    });
  };

  const handlePropertyChange = (properties: Property[]) =>
    onPropertyChange(
      {
        type: "compSetView/rentInsightsTab/estimatedUnitMix/changeProperty",
        data: {propertyIds: properties.map(property => property.id)}
      },
      properties
    );

  const filters: MixedFiltersArray = [
    getPropertyFilter({
      handlePropertyChange,
      properties,
      selectedProperties
    })
  ];

  return {
    canShowRenovationInfo,
    filterPopOverProps,
    filters,
    handleRentTypeChange,
    handleUnitRentGroupingChange,
    handlePropertyChange,
    isFilterPopOverOpen,
    isLoading,
    bedroomQuantities,
    rentType,
    showRenovationInfo,
    showUnitMixConfidenceColumn,
    toggleShowRenovationInfo,
    allUnitMixItems,
    filteredUnitMixItems,
    unitRentGrouping,
    arePropertiesLoading,
    properties,
    selectedProperties,
    selectProperties,
    reference,
    toggleFilterPopOver
  };
};

export {useUnitMixSection};
