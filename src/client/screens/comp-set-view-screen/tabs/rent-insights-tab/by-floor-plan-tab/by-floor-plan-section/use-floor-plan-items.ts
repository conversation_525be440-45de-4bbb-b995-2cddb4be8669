import * as withArray from "@unlockre/utils-array/dist";
import {useUrlQuery} from "@unlockre/utils-next/dist";
import {useMemo} from "react";

import * as withUrlQuery from "@/client/screens/comp-set-view-screen/url-query";
import {trpc} from "@/client/utils/trpc";
import {useAllCompSetComps} from "@/client/utils/trpc/use-all-comp-set-comps";
import type {
  ByFloorPlanUnitRentSummary,
  Property
} from "@/domain/domain-schema";
import {renovationStatuses} from "@/domain/renovation-status";
import type {KnownRenovationStatus} from "@/domain/renovation-status";
import {unitGroupings} from "@/domain/unit-grouping";

import * as withFloorPlanItem from "./floor-plan-item";

type UseUnitRentSummariesParams = {
  propertyIds?: string[];
  unitRenovationStatus?: KnownRenovationStatus;
};

const useUnitRentSummaries = ({
  propertyIds,
  unitRenovationStatus
}: UseUnitRentSummariesParams) => {
  const {dateFrom, dateTo} = useUrlQuery(withUrlQuery.getFrom).urlQuery;

  const unitRentSummariesResponse = trpc.getUnitRentSummaries.useQuery(
    {
      dateFrom,
      dateTo,
      propertyIds: propertyIds ?? [],
      unitGrouping: unitGroupings.byFloorPlan,
      unitRenovationStatus
    },
    {enabled: propertyIds !== undefined}
  );

  const unitRentSummaries = unitRentSummariesResponse.data as unknown as
    | ByFloorPlanUnitRentSummary[]
    | undefined;

  const groupedUnitRentSummaries = useMemo(
    () =>
      withArray.groupBy(
        unitRentSummaries ?? [],
        unitRentSummary => unitRentSummary.propertyId
      ),
    [unitRentSummaries]
  );

  const areUnitRentSummariesLoading =
    unitRentSummariesResponse.isInitialLoading;

  return [groupedUnitRentSummaries, areUnitRentSummariesLoading] as const;
};

const useComps = (compSetProperty?: Property) => {
  const {compSetId} = useUrlQuery(withUrlQuery.getFrom).urlQuery;

  const {areCompSetCompsLoading, compSetComps} = useAllCompSetComps({
    compRelations: [{name: "property"}],
    compSetId,
    compSetProperty
  });

  const compPropertyIds = useMemo(
    () => compSetComps?.map(comp => comp.propertyId),
    [compSetComps]
  );

  return {
    areCompsLoading: areCompSetCompsLoading,
    comps: compSetComps,
    compPropertyIds
  };
};

const useCompSet = () => {
  const {compSetId} = useUrlQuery(withUrlQuery.getFrom).urlQuery;

  const compSetResponse = trpc.getCompSet.useQuery({
    compSetId,
    relations: [{name: "property"}]
  });

  const compSet = compSetResponse.data;

  const isCompSetLoading = compSetResponse.isLoading;

  return {
    compSet,
    isCompSetLoading
  };
};

const useFloorPlanItems = () => {
  const {compSet, isCompSetLoading} = useCompSet();

  const {areCompsLoading, compPropertyIds, comps} = useComps(compSet?.property);

  const [allGroupedUnitRentSummaries, areAllUnitRentSummariesLoading] =
    useUnitRentSummaries({
      propertyIds: compPropertyIds
    });

  const [nonRenoGroupedUnitRentSummaries, areNonRenoUnitRentSummariesLoading] =
    useUnitRentSummaries({
      propertyIds: compPropertyIds,
      unitRenovationStatus: renovationStatuses.nonRenovated
    });

  const [renoGroupedUnitRentSummaries, areRenoUnitRentSummariesLoading] =
    useUnitRentSummaries({
      propertyIds: compPropertyIds,
      unitRenovationStatus: renovationStatuses.renovated
    });

  const floorPlanItems = useMemo(
    () =>
      comps
        ? comps.flatMap(comp =>
            withFloorPlanItem.createAllFrom({
              allUnitRentGroups:
                allGroupedUnitRentSummaries[comp.propertyId]?.[0]
                  ?.unitRentGroups ?? [],
              comp,
              nonRenoUnitRentGroups:
                nonRenoGroupedUnitRentSummaries[comp.propertyId]?.[0]
                  ?.unitRentGroups ?? [],
              renoUnitRentGroups:
                renoGroupedUnitRentSummaries[comp.propertyId]?.[0]
                  ?.unitRentGroups ?? []
            })
          )
        : [],
    [
      allGroupedUnitRentSummaries,
      comps,
      nonRenoGroupedUnitRentSummaries,
      renoGroupedUnitRentSummaries
    ]
  );

  const areFloorPlanItemsLoading =
    isCompSetLoading ||
    areCompsLoading ||
    areAllUnitRentSummariesLoading ||
    areNonRenoUnitRentSummariesLoading ||
    areRenoUnitRentSummariesLoading;

  return {
    areFloorPlanItemsLoading,
    floorPlanItems
  };
};

export {useFloorPlanItems};
