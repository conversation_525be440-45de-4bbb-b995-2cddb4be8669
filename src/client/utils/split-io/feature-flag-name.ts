const featureFlagNames = {
  allowSharingCompSets: "allowSharingCompSets",
  allowAddingMultipleProperties: "allowAddingMultipleProperties",
  generateXSLXFile: "generateXSLXFile",
  hasCustomSummaryExport: "hasCustomSummaryExport",
  showAffordabilityTag: "showAffordabilityTag",
  showByFloorPlanTab: "showByFloorPlanTab",
  showCopyPropertyIdButton: "showCopyPropertyIdButton",
  showEstimatedOccupancyColumn: "showEstimatedOccupancyColumn",
  showPricingRecommendations: "showPricingRecommendations",
  showPropertyQuality: "showPropertyQuality",
  showRenovationInfo: "showRenovationInfo",
  showReviewsTab: "showReviewsTab",
  showStrategyBadge: "showStrategyBadge",
  showUnitDetailsDrawer: "showUnitDetailsDrawer",
  showUnitMixConfidenceColumn: "showUnitMixConfidenceColumn",
  useConcessionsV2: "useConcessionsV2"
} as const;

type FeatureFlagName = (typeof featureFlagNames)[keyof typeof featureFlagNames];

export {featureFlagNames};
export type {FeatureFlagName};
