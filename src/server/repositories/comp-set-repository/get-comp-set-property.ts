import type {CompSelectorApiClient} from "@unlockre/comp-selector-api-tools/dist/comp-selector-api-client";
import type {KeyreviewApiClient} from "@unlockre/keyreview-api-tools/dist/keyreview-api-client";
import type {PropertyAssetsApiClient} from "@unlockre/property-assets-api-tools/dist/property-assets-api-client";
import type {RentApiClient} from "@unlockre/rent-api-tools/dist/rent-api-client";

import type {CompSetRelation} from "@/domain/comp-set-relation";
import {compSetRelationNames} from "@/domain/comp-set-relation-name";
import * as withRelation from "@/domain/relation";
import * as propertyRepository from "@/server/repositories/property-repository";

type Params = {
  compSelectorApiClient: CompSelectorApiClient;
  keyreviewApiClient: KeyreviewApiClient;
  propertyAssetsApiClient: PropertyAssetsApiClient;
  propertyId: string;
  relations: CompSetRelation[];
  rentApiClient: RentApiClient;
};

const getCompSetProperty = async ({relations, ...rest}: Params) => {
  const hasPropertyRelation = withRelation.includes(
    relations,
    compSetRelationNames.property
  );

  if (!hasPropertyRelation) {
    return undefined;
  }

  const property = await propertyRepository.getOperationalProperty({
    ...rest,
    relations: withRelation.unnestAllIn(
      relations,
      compSetRelationNames.property
    )
  });

  return property;
};

export {getCompSetProperty};
