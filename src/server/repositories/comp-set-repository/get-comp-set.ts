import type {CompSelectorApiClient} from "@unlockre/comp-selector-api-tools/dist/comp-selector-api-client";
import type {KeyreviewApiClient} from "@unlockre/keyreview-api-tools/dist/keyreview-api-client";
import {fetchEndpoint} from "@unlockre/open-api-client/dist";
import type {OrganizationsApiClient} from "@unlockre/organizations-api-tools/dist/organizations-api-client";
import type {PropertyAssetsApiClient} from "@unlockre/property-assets-api-tools/dist/property-assets-api-client";
import type {RentApiClient} from "@unlockre/rent-api-tools/dist/rent-api-client";

import type {CompSetRelation} from "@/domain/comp-set-relation";

import {createCompSetFrom} from "./create-comp-set-from";
import {getCompSetAccessPolicies} from "./get-comp-set-access-policies";
import {getCompSetOwner} from "./get-comp-set-owner";
import {getCompSetPermissions} from "./get-comp-set-permissions";
import {getCompSetProperty} from "./get-comp-set-property";
import {getCompSetReviewsProcess} from "./get-comp-set-reviews-process";

type Params = {
  compSelectorApiClient: CompSelectorApiClient;
  compSetId: string;
  keyreviewApiClient: KeyreviewApiClient;
  organizationsApiClient: OrganizationsApiClient;
  propertyAssetsApiClient: PropertyAssetsApiClient;
  relations?: CompSetRelation[];
  rentApiClient: RentApiClient;
  viewerOrganizationId: string;
};

// TODO: Implement comp relation logic (not yet required)
const getCompSet = async ({
  compSelectorApiClient,
  compSetId,
  keyreviewApiClient,
  organizationsApiClient,
  propertyAssetsApiClient,
  relations = [],
  rentApiClient,
  viewerOrganizationId
}: Params) => {
  const apiCompSetResponse = await fetchEndpoint(
    compSelectorApiClient,
    "/comp-sets/{compSetId}",
    "get",
    {compSetId}
  );

  const apiCompSet = apiCompSetResponse.data;

  const [accessPolicies, owner, property, permissions, reviewsProcess] =
    await Promise.all([
      getCompSetAccessPolicies({
        compSelectorApiClient,
        compSetId,
        organizationsApiClient,
        relations,
        viewerOrganizationId
      }),
      apiCompSet.createdBy
        ? getCompSetOwner({
            compSetOwnerId: apiCompSet.createdBy,
            organizationsApiClient,
            relations,
            viewerOrganizationId
          })
        : undefined,
      getCompSetProperty({
        compSelectorApiClient,
        keyreviewApiClient,
        propertyAssetsApiClient,
        propertyId: apiCompSet.basePropertyId,
        relations,
        rentApiClient
      }),
      getCompSetPermissions({
        compSelectorApiClient,
        compSetId,
        relations
      }),
      getCompSetReviewsProcess({
        compSetId,
        keyreviewApiClient,
        relations
      })
    ]);

  return createCompSetFrom({
    accessPolicies,
    apiCompSet,
    owner,
    permissions,
    property,
    reviewsProcess
  });
};

export {getCompSet};
