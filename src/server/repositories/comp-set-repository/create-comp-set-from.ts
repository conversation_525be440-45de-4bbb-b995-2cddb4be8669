import type {CompSetPermission} from "@/domain/comp-set-permission";
import type {
  CompSet,
  CompSetAccessPolicy,
  CompSetReviewsProcess,
  Property,
  RelatedUser
} from "@/domain/domain-schema";

import type {ApiComparablePropertySet} from "./types";

type Params = {
  accessPolicies?: CompSetAccessPolicy[];
  apiCompSet: ApiComparablePropertySet;
  owner?: RelatedUser;
  permissions?: CompSetPermission[];
  property?: Property;
  reviewsProcess?: CompSetReviewsProcess;
};

const createCompSetFrom = ({apiCompSet, ...rest}: Params): CompSet => ({
  compsTotal: apiCompSet.propertyIds.length,
  createdAt: apiCompSet.createdAt,
  id: apiCompSet.id,
  name: apiCompSet.name,
  propertyAcquisitionDate: apiCompSet.basePropertyAcquisitionDate,
  propertyId: apiCompSet.basePropertyId,
  propertyStage: apiCompSet.basePropertyStage,
  updatedAt: apiCompSet.updatedAt,
  ...rest
});

export {createCompSetFrom};
