{"name": "keycomps", "repository": "**************:unlockre/keycomps.git", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint:js": "eslint", "lint:ts": "eslint --config ./.eslintrc.tsc.js", "postinstall": "husky"}, "dependencies": {"@amplitude/analytics-browser": "^2.8.1", "@auth0/auth0-react": "^2.2.4", "@auth0/auth0-spa-js": "^2.1.3", "@fontsource-variable/inter": "^5.2.5", "@math.gl/core": "^4.0.1", "@math.gl/web-mercator": "^4.0.1", "@nivo/axes": "^0.88.0", "@nivo/bar": "^0.88.0", "@nivo/core": "^0.88.0", "@nivo/line": "^0.88.0", "@phosphor-icons/react": "^2.1.5", "@react-google-maps/api": "^2.19.3", "@splitsoftware/splitio": "^10.26.0", "@splitsoftware/splitio-react": "^1.12.0", "@szhsin/react-menu": "^3.5.0", "@tanstack/react-query": "^4.36.1", "@trpc/client": "^10.45.2", "@trpc/next": "^10.45.2", "@trpc/react-query": "^10.45.2", "@trpc/server": "^10.45.2", "@unlockre/comp-selector-api-tools": "^4.3.0", "@unlockre/components-library": "^50.0.0", "@unlockre/graph-tools": "^1.5.2", "@unlockre/keyos-tools": "^8.0.2", "@unlockre/keyreview-api-tools": "^1.13.0", "@unlockre/map-tools": "^2.4.1", "@unlockre/open-api-client": "^4.0.0", "@unlockre/organizations-api-tools": "^4.0.0", "@unlockre/page-tools": "^3.0.3", "@unlockre/pdf-export-tools": "^2.0.0", "@unlockre/property-assets-api-tools": "^8.2.0", "@unlockre/rent-api-tools": "^9.0.0", "@unlockre/utils-amplitude": "^5.0.0", "@unlockre/utils-array": "^1.10.0", "@unlockre/utils-auth0": "^9.1.1", "@unlockre/utils-date": "^1.3.0", "@unlockre/utils-dom": "^1.1.0", "@unlockre/utils-formatting": "4.0.0", "@unlockre/utils-http": "^1.0.0", "@unlockre/utils-next": "^3.0.0", "@unlockre/utils-number": "^2.2.0", "@unlockre/utils-object": "^3.0.0", "@unlockre/utils-react": "^3.0.0", "@unlockre/utils-split-io": "^3.0.0", "@unlockre/utils-string": "^1.2.0", "@unlockre/utils-userback": "^2.3.1", "@unlockre/utils-validation": "^2.0.0", "@unlockre/whats-new-api-tools": "^1.0.0", "@unlockre/widgets-library": "^4.0.5", "color-alpha": "^2.0.0", "exceljs": "^4.4.0", "fast-equals": "^5.2.2", "json-2-csv": "^5.5.1", "jwt-decode": "^4.0.0", "merge-refs": "^1.3.0", "mime-db": "^1.52.0", "next": "^15.2.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-static-google-map": "^0.9.0", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "styled-components": "^5.3.11", "swr": "^2.3.3", "use-debounce": "^10.0.1", "use-resize-observer": "^9.1.0", "zod": "^3.23.8"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@tsconfig/strictest": "^2.0.5", "@types/google.maps": "^3.58.1", "@types/mime-db": "^1.43.5", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.5", "@types/styled-components": "^5.1.34", "@typescript-eslint/parser": "^7.13.0", "@unlockre/eslint-config": "^2.2.0", "@unlockre/property-search-api-tools": "^1.1.0", "eslint": "^8.57.0", "eslint-plugin-tsc": "^2.0.0", "husky": "^9.0.11", "lint-staged": "^15.2.6", "next-images": "^1.8.5", "prettier": "^3.3.2", "semantic-release": "^24.2.1", "semantic-release-slack-bot": "^4.0.2", "typescript": "5.4.5"}, "resolutions": {"@mui/styled-engine": "npm:@mui/styled-engine-sc@^5.0.0", "@types/react": "^18.0.0", "d3-delaunay": "^5.0.0", "d3-color": "^2.0.0", "d3-interpolate": "^2.0.0", "d3-shape": "^2.0.0", "d3-scale": "^3.0.0", "d3-scale-chromatic": "^2.0.0", "react": "^18.0.0"}, "packageManager": "yarn@3.8.2"}