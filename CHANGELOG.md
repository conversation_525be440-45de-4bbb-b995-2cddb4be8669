## [1.39.1](https://github.com/unlockre/keycomps/compare/v1.39.0...v1.39.1) (2025-06-10)


### Bug Fixes

* Correct concessions not showing properly ([#753](https://github.com/unlockre/keycomps/issues/753)) ([ecb12c6](https://github.com/unlockre/keycomps/commit/ecb12c6ec26c1ad7a6010b1b73f1ec51760c1afa))

# [1.39.0](https://github.com/unlockre/keycomps/compare/v1.38.0...v1.39.0) (2025-06-09)


### Bug Fixes

* Add property badges to compset edit screen ([#746](https://github.com/unlockre/keycomps/issues/746)) ([37f1924](https://github.com/unlockre/keycomps/commit/37f19244567d96a3596f7dc2bd5cd15a68f17a1b))
* Fix stg-cd github workflow ([#747](https://github.com/unlockre/keycomps/issues/747)) ([db42c57](https://github.com/unlockre/keycomps/commit/db42c579dbc7a4b1275cb130be077f358f377c04))
* Hide all false benefits column ([#742](https://github.com/unlockre/keycomps/issues/742)) ([2fe3ba0](https://github.com/unlockre/keycomps/commit/2fe3ba0e4e9b0d43187d0dd1e88e5582ed4426e4))
* Rename est occupancy to leased ([#736](https://github.com/unlockre/keycomps/issues/736)) ([c6ddd86](https://github.com/unlockre/keycomps/commit/c6ddd86f2836b0bb99c1033131ac0b91188205aa))
* Update gradient lines position ([#745](https://github.com/unlockre/keycomps/issues/745)) ([93e36ba](https://github.com/unlockre/keycomps/commit/93e36bab179189d4e4f0cbbb0059d6787ca1e9ae))
* Update listing range overlaps ([#749](https://github.com/unlockre/keycomps/issues/749)) ([88db8f8](https://github.com/unlockre/keycomps/commit/88db8f8f2e1df26db2d4eb31cb200de044190ed5))


### Features

* Add createDevelopingProperty procedure ([#739](https://github.com/unlockre/keycomps/issues/739)) ([f8e5c35](https://github.com/unlockre/keycomps/commit/f8e5c350d2f24732559d2abce453c4e452884231))
* Add floor prop to unit detail ([#740](https://github.com/unlockre/keycomps/issues/740)) ([0b1d6ea](https://github.com/unlockre/keycomps/commit/0b1d6eac3f1f666988db18c1367996adcde6f602))
* Allow Urban Core organization to download Keyway excel ([#744](https://github.com/unlockre/keycomps/issues/744)) ([c1ec28a](https://github.com/unlockre/keycomps/commit/c1ec28afc7bff0249ed61099f80f19eb37fef210))
* Integrate comp-selector-api property endpoints ([#737](https://github.com/unlockre/keycomps/issues/737)) ([73d3066](https://github.com/unlockre/keycomps/commit/73d3066d757a38ed330290dda4e20639c154b677))
* Update historical charts when there is no data ([#727](https://github.com/unlockre/keycomps/issues/727)) ([7b234bf](https://github.com/unlockre/keycomps/commit/7b234bfe58bd929a48402adc94f4c44beea888ab))

# [1.38.0](https://github.com/unlockre/keycomps/compare/v1.37.2...v1.38.0) (2025-05-29)


### Bug Fixes

* Add format currency ([#713](https://github.com/unlockre/keycomps/issues/713)) ([01e00ca](https://github.com/unlockre/keycomps/commit/01e00caa5574a948a0964b4a306a6b17c859ccff))
* Amenities section style ([#718](https://github.com/unlockre/keycomps/issues/718)) ([5c05fec](https://github.com/unlockre/keycomps/commit/5c05fec56eb62ac00dbfc300b8ec8493e1ee8985))
* Fix Drawer closing issue ([#721](https://github.com/unlockre/keycomps/issues/721)) ([14f3212](https://github.com/unlockre/keycomps/commit/14f3212f9b7bcdb3b1da8809f3cf7c262b4221a0))
* Fix menu layering ([#711](https://github.com/unlockre/keycomps/issues/711)) ([db6b092](https://github.com/unlockre/keycomps/commit/db6b092fbac3c6d806bf17834167f66f7cb96bac))
* Fix VirtualTable loading issues ([#716](https://github.com/unlockre/keycomps/issues/716)) ([ff3bb7f](https://github.com/unlockre/keycomps/commit/ff3bb7f7e4f1b99d5d6f4f35e55d2e125e34691f))
* Revert leaseup filter ([#715](https://github.com/unlockre/keycomps/issues/715)) ([1439d9c](https://github.com/unlockre/keycomps/commit/1439d9ca70edda8b8048fab2660a54e2d6b095c7))
* Update amenities section ([#712](https://github.com/unlockre/keycomps/issues/712)) ([f6c1361](https://github.com/unlockre/keycomps/commit/f6c1361edca48feaddf4ad25e75e5d6bac130b8a))
* Update dates unit drawer  ([#719](https://github.com/unlockre/keycomps/issues/719)) ([23fc184](https://github.com/unlockre/keycomps/commit/23fc18447775b6d996a8a2175acacfb716ae5871))
* Update dates unit drawer ([#710](https://github.com/unlockre/keycomps/issues/710)) ([64e6b23](https://github.com/unlockre/keycomps/commit/64e6b23aa2f3498a9e0070c969e0435fa608b1c1))
* Update graph when dates change ([#725](https://github.com/unlockre/keycomps/issues/725)) ([da63306](https://github.com/unlockre/keycomps/commit/da63306b17ff4eaa0a94249268148b7ab67079a2))


### Features

* Add history section to detail drawer ([#698](https://github.com/unlockre/keycomps/issues/698)) ([ca978d1](https://github.com/unlockre/keycomps/commit/ca978d100aa547f173ffbc37e9711e9c5740589d))
* Add leaseup filter ([#706](https://github.com/unlockre/keycomps/issues/706)) ([d565157](https://github.com/unlockre/keycomps/commit/d56515787663843a1fb5506840b8c716c185e028))
* Add show benefits checkbox and column hiding capabilities ([#720](https://github.com/unlockre/keycomps/issues/720)) ([0e3aeff](https://github.com/unlockre/keycomps/commit/0e3aeffb2c79784094c05751eb0a3b51c7a274f9))
* Add support for concessions v2 ([#728](https://github.com/unlockre/keycomps/issues/728)) ([545e089](https://github.com/unlockre/keycomps/commit/545e089f79f81852caec8dd81bcf8c040f3ed60a))
* Change of target on the cancel button of compset editor ([#708](https://github.com/unlockre/keycomps/issues/708)) ([9fc9ee9](https://github.com/unlockre/keycomps/commit/9fc9ee9b38a58814c7470c0b8145ccb7b2ec69be))
* Update @unlockre/utils-array dependency ([#723](https://github.com/unlockre/keycomps/issues/723)) ([8fa5eff](https://github.com/unlockre/keycomps/commit/8fa5eff9b3da26d4f397e6b67e1bfe8a1e93181f))
* Update drawer container ([#705](https://github.com/unlockre/keycomps/issues/705)) ([88a7821](https://github.com/unlockre/keycomps/commit/88a7821ee8b7e318cdbcc61bf6d0779c389a00b2))
* Update no amenity icon ([#707](https://github.com/unlockre/keycomps/issues/707)) ([c14daf5](https://github.com/unlockre/keycomps/commit/c14daf5bfae2a991238165041a9f66f92cdb2da2))

## [1.37.2](https://github.com/unlockre/keycomps/compare/v1.37.1...v1.37.2) (2025-05-28)


### Bug Fixes

* Fix Keyway excel ([#730](https://github.com/unlockre/keycomps/issues/730)) ([f0b4ee9](https://github.com/unlockre/keycomps/commit/f0b4ee9a21d6aef185ba402ed6e1eb38a6fc643d))

## [1.37.1](https://github.com/unlockre/keycomps/compare/v1.37.0...v1.37.1) (2025-05-27)


### Bug Fixes

* Fix Artemis excel ([#724](https://github.com/unlockre/keycomps/issues/724)) ([3a108a0](https://github.com/unlockre/keycomps/commit/3a108a06be77be047e3d94dfa7dd7f98180c632d))

# [1.37.0](https://github.com/unlockre/keycomps/compare/v1.36.0...v1.37.0) (2025-05-16)


### Bug Fixes

* Copy update in add multiple properties modal ([#660](https://github.com/unlockre/keycomps/issues/660)) ([88b7b8f](https://github.com/unlockre/keycomps/commit/88b7b8f652ed7ec00f3b401a0610cab6d572e385))
* Fix sharing issues ([#703](https://github.com/unlockre/keycomps/issues/703)) ([6e0ed4f](https://github.com/unlockre/keycomps/commit/6e0ed4f6ae4b46eac9b2e0bb676e58d06f61e38d))
* Move drawer ff to the table ([#675](https://github.com/unlockre/keycomps/issues/675)) ([a08ecfb](https://github.com/unlockre/keycomps/commit/a08ecfbf38ae25ce3ad4e719414a87d51854c4b8))
* Protect acquisition date changing ([#702](https://github.com/unlockre/keycomps/issues/702)) ([048d528](https://github.com/unlockre/keycomps/commit/048d528f9e43594cf8554f2af42508e58459a2e9))
* Removal of decimals in recommended price modal ([#663](https://github.com/unlockre/keycomps/issues/663)) ([3cdcc6d](https://github.com/unlockre/keycomps/commit/3cdcc6da5b653600a86a8ee0b9068a928f11f6cb))
* Remove reno column from by floor plan ([#694](https://github.com/unlockre/keycomps/issues/694)) ([001507d](https://github.com/unlockre/keycomps/commit/001507d169fe19098cd5ebbb40e00ae534311be3))
* Sidepanel target link ([#657](https://github.com/unlockre/keycomps/issues/657)) ([aa7a4e2](https://github.com/unlockre/keycomps/commit/aa7a4e2faf23119553d3e78c4171f1f7656878ad))


### Features

* Add acquisition date selector ([#687](https://github.com/unlockre/keycomps/issues/687)) ([f8a9c2f](https://github.com/unlockre/keycomps/commit/f8a9c2f33763ad752bfa3c61b6bb9a3148e12e54))
* Add acquisition date to home table ([#699](https://github.com/unlockre/keycomps/issues/699)) ([b5b59fb](https://github.com/unlockre/keycomps/commit/b5b59fb0f43b5da038e201bf4eb0245595801b3f))
* Add amenities section ([#683](https://github.com/unlockre/keycomps/issues/683)) ([4850e7c](https://github.com/unlockre/keycomps/commit/4850e7c50b831c6d0afe4c4f06506c79cc3657b6))
* Add basic info to unit detail drawer ([#676](https://github.com/unlockre/keycomps/issues/676)) ([715529a](https://github.com/unlockre/keycomps/commit/715529aea45bbc706492acd5649ff747ab6aa139))
* Add CompSet relations ([#697](https://github.com/unlockre/keycomps/issues/697)) ([227d0d7](https://github.com/unlockre/keycomps/commit/227d0d75d21fb208d0373d02d6d9d2c272cea7e6))
* Add concessions section to unit drawer ([#688](https://github.com/unlockre/keycomps/issues/688)) ([7047218](https://github.com/unlockre/keycomps/commit/704721811075a803469ce3ea34a578badeb60655))
* Add is active badge ([#666](https://github.com/unlockre/keycomps/issues/666)) ([649e9b5](https://github.com/unlockre/keycomps/commit/649e9b553fd9fbb8a7acb14bd5b915f0775107fa))
* Add listing section to unit drawer ([#680](https://github.com/unlockre/keycomps/issues/680)) ([46325f1](https://github.com/unlockre/keycomps/commit/46325f163c5eeb333acc86211f0a66a461b5f589))
* Add rent row to listing section ([#689](https://github.com/unlockre/keycomps/issues/689)) ([43e1baa](https://github.com/unlockre/keycomps/commit/43e1baaba9a654ceb5046eba9a48b11935b1f88e))
* Add ShareCompSetDialog component and related logic ([#700](https://github.com/unlockre/keycomps/issues/700)) ([d2879ef](https://github.com/unlockre/keycomps/commit/d2879ef5a82b1ef2c114ad9c33ef357134588c9f))
* Add unit detail drawer ([#667](https://github.com/unlockre/keycomps/issues/667)) ([1594c2c](https://github.com/unlockre/keycomps/commit/1594c2cc7389fd59a6b8374a602ef49187c71365))
* Add view only acquisition date ([#695](https://github.com/unlockre/keycomps/issues/695)) ([3578ba2](https://github.com/unlockre/keycomps/commit/3578ba2aabfd2da3dd55166ed8bc0f9a5d473364))
* Hide unit details drawer under ff ([#674](https://github.com/unlockre/keycomps/issues/674)) ([ae990fd](https://github.com/unlockre/keycomps/commit/ae990fd495e830b9beef494110aa3269a01bb4ba))
* Move on market columns to by unit ([#661](https://github.com/unlockre/keycomps/issues/661)) ([bbe3f0a](https://github.com/unlockre/keycomps/commit/bbe3f0a06b9f1eed8100df5ef63a193f8aa87890))
* Replace rent type dropdown with a segmentedcontrol ([#662](https://github.com/unlockre/keycomps/issues/662)) ([b5911b4](https://github.com/unlockre/keycomps/commit/b5911b4d6db70ff894a417b8e6330195f79463c4))
* Support no comp set creation flow ([#677](https://github.com/unlockre/keycomps/issues/677)) ([b928ecf](https://github.com/unlockre/keycomps/commit/b928ecf96e1ed4c1f09551e115138461f6e38c2a))

# [1.36.0](https://github.com/unlockre/keycomps/compare/v1.35.1...v1.36.0) (2025-05-09)


### Features

* Logout if needed ([#690](https://github.com/unlockre/keycomps/issues/690)) ([9ba0ed3](https://github.com/unlockre/keycomps/commit/9ba0ed3df9ddd5f9fb35b95d5734faab6e567742))

## [1.35.1](https://github.com/unlockre/keycomps/compare/v1.35.0...v1.35.1) (2025-05-07)


### Bug Fixes

* Correct reno status unit ([#684](https://github.com/unlockre/keycomps/issues/684)) ([8f03ca6](https://github.com/unlockre/keycomps/commit/8f03ca6649cfa108267dd3a7625093f611831ef1))
* Revert reno column removal on by floor plan ([#686](https://github.com/unlockre/keycomps/issues/686)) ([286102d](https://github.com/unlockre/keycomps/commit/286102d86a6342cafddc3403d9da5f5566431140))

# [1.35.0](https://github.com/unlockre/keycomps/compare/v1.34.2...v1.35.0) (2025-05-07)


### Features

* Delete reno columns from by floor plan ([#678](https://github.com/unlockre/keycomps/issues/678)) ([37f7a5d](https://github.com/unlockre/keycomps/commit/37f7a5d5554b6c5864ec216bfb30c3cef6c0e7f8))

## [1.34.2](https://github.com/unlockre/keycomps/compare/v1.34.1...v1.34.2) (2025-04-29)


### Bug Fixes

* Fix selectedPropertyIds url query param ([#670](https://github.com/unlockre/keycomps/issues/670)) ([f0ca991](https://github.com/unlockre/keycomps/commit/f0ca991408866e421bf35a265905151436b2b8dd))

## [1.34.1](https://github.com/unlockre/keycomps/compare/v1.34.0...v1.34.1) (2025-04-28)


### Bug Fixes

* Map unknown to nonreno pill ([#664](https://github.com/unlockre/keycomps/issues/664)) ([5c060e8](https://github.com/unlockre/keycomps/commit/5c060e80f0fc2f72b2bc486ca9ff8ebf5d3d9c6e))

# [1.34.0](https://github.com/unlockre/keycomps/compare/v1.33.0...v1.34.0) (2025-04-23)


### Bug Fixes

* Correct 6 month restriction issues ([#654](https://github.com/unlockre/keycomps/issues/654)) ([119a875](https://github.com/unlockre/keycomps/commit/119a8753ad5532bc42bb04ea8a99444c383bfe01))
* Correct affordable tooltip ([#656](https://github.com/unlockre/keycomps/issues/656)) ([58e6abb](https://github.com/unlockre/keycomps/commit/58e6abb59576327b4a5ba94a1ac5d9c44596e37b))
* Correct dropdown overlapping on reviews ([#642](https://github.com/unlockre/keycomps/issues/642)) ([603b0ef](https://github.com/unlockre/keycomps/commit/603b0ef42d6c60d5bb7ed8e380368a74146b73d6))
* Correct is affordable filter ([#658](https://github.com/unlockre/keycomps/issues/658)) ([cfaf09b](https://github.com/unlockre/keycomps/commit/cfaf09b998d49c303c8ffffefbec07ae764ea6d1))
* Correct list items markup bug ([#653](https://github.com/unlockre/keycomps/issues/653)) ([0e113eb](https://github.com/unlockre/keycomps/commit/0e113eb842267cc6af164fcbe32cac3e97834b0c))
* Remove parenthesis in tooltip ([#650](https://github.com/unlockre/keycomps/issues/650)) ([0ea7c58](https://github.com/unlockre/keycomps/commit/0ea7c58b6502f046e22a2f851f189abed858faba))


### Features

* Add ff for affordability ([#652](https://github.com/unlockre/keycomps/issues/652)) ([0717457](https://github.com/unlockre/keycomps/commit/071745789a6971ecbc16b5c447547010a0c3aa4c))
* Non-reno icon ([#641](https://github.com/unlockre/keycomps/issues/641)) ([299bf1e](https://github.com/unlockre/keycomps/commit/299bf1ea1dc5b9758ea5ce25ca6cee58b5e20d0b))

# [1.33.0](https://github.com/unlockre/keycomps/compare/v1.32.1...v1.33.0) (2025-04-21)


### Bug Fixes

* Bump clib ([#639](https://github.com/unlockre/keycomps/issues/639)) ([9d1b268](https://github.com/unlockre/keycomps/commit/9d1b268bd7b3ba21ab38da91b80548241c0faa96))
* Correct icon shrinking and header alignment ([#638](https://github.com/unlockre/keycomps/issues/638)) ([70dc9ea](https://github.com/unlockre/keycomps/commit/70dc9eab49f234a40de44547f0766caeafce658b))
* Limit analysis range to six month ([#647](https://github.com/unlockre/keycomps/issues/647)) ([316bd3b](https://github.com/unlockre/keycomps/commit/316bd3bee2377ca86e83ce7e0d0f80564863475e))
* Temporarily comment affordability ([#651](https://github.com/unlockre/keycomps/issues/651)) ([99a0097](https://github.com/unlockre/keycomps/commit/99a00978cb3e0322246a84eea54efa26a93b3f8d))


### Features

* Add affordability tag ([#620](https://github.com/unlockre/keycomps/issues/620)) ([8fd8669](https://github.com/unlockre/keycomps/commit/8fd8669f009cdbda6075971761ec766415616baf))
* Make rent insights sortables ([#635](https://github.com/unlockre/keycomps/issues/635)) ([317eb55](https://github.com/unlockre/keycomps/commit/317eb558768e7acdb57e993958b798ff13b35b56))

## [1.32.1](https://github.com/unlockre/keycomps/compare/v1.32.0...v1.32.1) (2025-04-17)


### Bug Fixes

* Correct wrong mapping on full list ([#644](https://github.com/unlockre/keycomps/issues/644)) ([f967cdf](https://github.com/unlockre/keycomps/commit/f967cdfb5a6d51b299751e657ac54c98ec2c20ff))

# [1.32.0](https://github.com/unlockre/keycomps/compare/v1.31.1...v1.32.0) (2025-04-15)


### Bug Fixes

* Correct compset image indicator ([#616](https://github.com/unlockre/keycomps/issues/616)) ([0dbb944](https://github.com/unlockre/keycomps/commit/0dbb9440d1d8fa6ee1adc379e95ced1bd5045d19))
* Correct filters wrapping and improve pills ([#631](https://github.com/unlockre/keycomps/issues/631)) ([63a8dbc](https://github.com/unlockre/keycomps/commit/63a8dbcacd88ffe4b346d805c29186dbbb63b446))
* Correct grouping column issues ([#622](https://github.com/unlockre/keycomps/issues/622)) ([bf95639](https://github.com/unlockre/keycomps/commit/bf9563951cbf46af3e7d067d012fa24a547dd077))
* Correct historical rent chart labels ([#623](https://github.com/unlockre/keycomps/issues/623)) ([1af64fb](https://github.com/unlockre/keycomps/commit/1af64fb40258947ccb0566eb5ac86a7ade00fda1))
* Correct rating column position and mix confidence column width ([#614](https://github.com/unlockre/keycomps/issues/614)) ([3bd8645](https://github.com/unlockre/keycomps/commit/3bd8645b0fde2711fa4f0f1137e69e1f44bb1004))
* Correct rent graph dropdown styles ([#617](https://github.com/unlockre/keycomps/issues/617)) ([99926a3](https://github.com/unlockre/keycomps/commit/99926a31acec3cda0cc9044a64e2bd4525cb6a68))
* Correct wrapping over filters ([#630](https://github.com/unlockre/keycomps/issues/630)) ([8721634](https://github.com/unlockre/keycomps/commit/87216341fbdd5b79688f0fe466995ac8cfc38042))
* Fix stories range filter enabled counter ([#636](https://github.com/unlockre/keycomps/issues/636)) ([055d21a](https://github.com/unlockre/keycomps/commit/055d21a9858863b24299ee686feb1e4da2ea4aa7))
* Support more bathrooms ([#634](https://github.com/unlockre/keycomps/issues/634)) ([146c586](https://github.com/unlockre/keycomps/commit/146c5869cbc25edba865a1d47d055239c8a83273))


### Features

* Add default 30 days to date selector ([#628](https://github.com/unlockre/keycomps/issues/628)) ([8db353c](https://github.com/unlockre/keycomps/commit/8db353c00bc0c7739d1496357527320a9d905a7d))
* Add deprecation warning message for listing ([#629](https://github.com/unlockre/keycomps/issues/629)) ([a52aa40](https://github.com/unlockre/keycomps/commit/a52aa40823773a1d1d3ba5b715b3d871c211d3fc))
* Add multiselect to filters ([#627](https://github.com/unlockre/keycomps/issues/627)) ([39f6097](https://github.com/unlockre/keycomps/commit/39f6097a8cc877b2c968b92e76e29d94dac52dd0))
* Group rent insights by unit type columns ([#621](https://github.com/unlockre/keycomps/issues/621)) ([7f1705b](https://github.com/unlockre/keycomps/commit/7f1705b0289bb9607d76afba9f7ac4427c4b1f83))
* Update next ([#618](https://github.com/unlockre/keycomps/issues/618)) ([641a27d](https://github.com/unlockre/keycomps/commit/641a27d13214c5bc5fd20ff6168e5e998499d7d3))

## [1.31.1](https://github.com/unlockre/keycomps/compare/v1.31.0...v1.31.1) (2025-04-09)


### Bug Fixes

* Correct amenities not showing when no compset ([#625](https://github.com/unlockre/keycomps/issues/625)) ([dbf7954](https://github.com/unlockre/keycomps/commit/dbf7954902501d3f4559412544d0c341c3e7b116))

# [1.31.0](https://github.com/unlockre/keycomps/compare/v1.30.1...v1.31.0) (2025-03-27)


### Bug Fixes

* Add missing style to segmented control ([#608](https://github.com/unlockre/keycomps/issues/608)) ([5058df3](https://github.com/unlockre/keycomps/commit/5058df343adf3f7b92af759f68d95b3940322fc3))
* Add stories to compset header ([#593](https://github.com/unlockre/keycomps/issues/593)) ([f62c826](https://github.com/unlockre/keycomps/commit/f62c82622cc4b52a7f4d6bfc26f5022d7b78fd35))


### Features

* Add description to column headers ([#601](https://github.com/unlockre/keycomps/issues/601)) ([f9680c3](https://github.com/unlockre/keycomps/commit/f9680c3c30959efe9a7b83148dd606e17e451cb1))
* Add tooltip to new stories column ([#611](https://github.com/unlockre/keycomps/issues/611)) ([11e9506](https://github.com/unlockre/keycomps/commit/11e95069a75f4aacb5ca7f43f2ebbe19c64e6adb))
* Block new compset until a property is selected ([#609](https://github.com/unlockre/keycomps/issues/609)) ([5249439](https://github.com/unlockre/keycomps/commit/5249439a04c22e53f9de6857f3782b6225427be4))
* Remove heating amenity and add terrace ([#586](https://github.com/unlockre/keycomps/issues/586)) ([4766c71](https://github.com/unlockre/keycomps/commit/4766c71aa9c26c2d33a0d519791523037e81f896))
* Update historical rent graph ([#558](https://github.com/unlockre/keycomps/issues/558)) ([f8d2f99](https://github.com/unlockre/keycomps/commit/f8d2f996a4780aa41e5e70291481b01291899991))
* Use FF for strategy badges ([#610](https://github.com/unlockre/keycomps/issues/610)) ([c386ca1](https://github.com/unlockre/keycomps/commit/c386ca1ef453a72371ddc4badf8be2eb1ecab601))

## [1.30.1](https://github.com/unlockre/keycomps/compare/v1.30.0...v1.30.1) (2025-03-26)


### Bug Fixes

* Fix comp-set-edit-screen filters layout ([#605](https://github.com/unlockre/keycomps/issues/605)) ([eb40ba4](https://github.com/unlockre/keycomps/commit/eb40ba4826609aed2616533a631d397614979faa))
* Hide PropertyStrategyBadge ([#606](https://github.com/unlockre/keycomps/issues/606)) ([643b43a](https://github.com/unlockre/keycomps/commit/643b43a6b33b09533f87bdf569e0a96739d3f3be))

# [1.30.0](https://github.com/unlockre/keycomps/compare/v1.29.0...v1.30.0) (2025-03-25)


### Bug Fixes

* Fix avgDaysOnMarket column ([#600](https://github.com/unlockre/keycomps/issues/600)) ([15ffa3b](https://github.com/unlockre/keycomps/commit/15ffa3b8d4de47d6ec2d8592d453a5f5642b2792))
* Improve other comps filters layout ([#602](https://github.com/unlockre/keycomps/issues/602)) ([989b466](https://github.com/unlockre/keycomps/commit/989b466c59de16ea63560bd7815af4a606d40cfe))


### Features

* Add onlyWithRent comps filtering ([#597](https://github.com/unlockre/keycomps/issues/597)) ([f65de2e](https://github.com/unlockre/keycomps/commit/f65de2e9f62dfed845e44fa5c359f87dcf43a904))
* Add stories comps filtering ([#595](https://github.com/unlockre/keycomps/issues/595)) ([bb7fd4c](https://github.com/unlockre/keycomps/commit/bb7fd4cb6c376a5854ca7cf32dc6634468144c07))

# [1.29.0](https://github.com/unlockre/keycomps/compare/v1.28.1...v1.29.0) (2025-03-20)


### Bug Fixes

* Fix estimatedOccupancyCompColumn logic ([#598](https://github.com/unlockre/keycomps/issues/598)) ([236f875](https://github.com/unlockre/keycomps/commit/236f875a48c9c0d2b7fe07a8a187de72c3b79fd3))


### Features

* Add housing segments comps filtering ([#591](https://github.com/unlockre/keycomps/issues/591)) ([baa44af](https://github.com/unlockre/keycomps/commit/baa44aff18c4a55a9e889876dbcfcef51c35641d))

## [1.28.1](https://github.com/unlockre/keycomps/compare/v1.28.0...v1.28.1) (2025-03-18)


### Bug Fixes

* Correct reviews sentiment analysis table ([#590](https://github.com/unlockre/keycomps/issues/590)) ([d29fd2e](https://github.com/unlockre/keycomps/commit/d29fd2e4f9bf756da2cd324652a3f0e4fcb316ef))

# [1.28.0](https://github.com/unlockre/keycomps/compare/v1.27.1...v1.28.0) (2025-03-13)


### Bug Fixes

* Disable camera control in compset view screen ([#581](https://github.com/unlockre/keycomps/issues/581)) ([982d60b](https://github.com/unlockre/keycomps/commit/982d60bf619aa5e09ad436c1c421b68322e55a6a))


### Features

* Add housing and strategy badges ([#583](https://github.com/unlockre/keycomps/issues/583)) ([aa1ff41](https://github.com/unlockre/keycomps/commit/aa1ff418a11e4f6a3d5db0011554f2edb547b2b9))
* Add stories column and property style badge ([#580](https://github.com/unlockre/keycomps/issues/580)) ([430de9b](https://github.com/unlockre/keycomps/commit/430de9b8b1f50fa9758012c297da0d387e144dba)), closes [#566](https://github.com/unlockre/keycomps/issues/566)
* Add whats new modal ([#566](https://github.com/unlockre/keycomps/issues/566)) ([0ee7a9d](https://github.com/unlockre/keycomps/commit/0ee7a9d37570723b0141e644b825c23dca9dcefe))
* Update widgets library for whats new scrollbar ([#579](https://github.com/unlockre/keycomps/issues/579)) ([f68a084](https://github.com/unlockre/keycomps/commit/f68a084674eff79894f99707b79a8806cabb183a))

## [1.27.1](https://github.com/unlockre/keycomps/compare/v1.27.0...v1.27.1) (2025-03-05)


### Bug Fixes

* Fix PropertyCompsFilters zod schema ([#577](https://github.com/unlockre/keycomps/issues/577)) ([cfc89ca](https://github.com/unlockre/keycomps/commit/cfc89ca66bd0b99a541b4924df1ab6bc034536ee))
* Use unique listings for number of units ([#573](https://github.com/unlockre/keycomps/issues/573)) ([18bf7c7](https://github.com/unlockre/keycomps/commit/18bf7c7618b61b459e5a4abe3958244cf651d6c5))

# [1.27.0](https://github.com/unlockre/keycomps/compare/v1.26.1...v1.27.0) (2025-03-02)


### Bug Fixes

* Handle no unitMixItems ([#576](https://github.com/unlockre/keycomps/issues/576)) ([b2e2210](https://github.com/unlockre/keycomps/commit/b2e22107dcdcecfb6a7640584ec6b1d12ed8933b))


### Features

* Add avg sqft columns ([#574](https://github.com/unlockre/keycomps/issues/574)) ([bffca10](https://github.com/unlockre/keycomps/commit/bffca1030e0dcb60e1aa5e078b4a894e91a707bd))

## [1.26.1](https://github.com/unlockre/keycomps/compare/v1.26.0...v1.26.1) (2025-02-28)


### Bug Fixes

* Add keyway excel exporter ([#570](https://github.com/unlockre/keycomps/issues/570)) ([1e66e29](https://github.com/unlockre/keycomps/commit/1e66e29a4fc3598f23dab5e2e377227d29712b51))

# [1.26.0](https://github.com/unlockre/keycomps/compare/v1.25.0...v1.26.0) (2025-02-28)


### Features

* Add new intercapital excel features ([#563](https://github.com/unlockre/keycomps/issues/563)) ([0131be5](https://github.com/unlockre/keycomps/commit/0131be5a36a06d3e4658b046d8ffef8b0320ba0d))

# [1.25.0](https://github.com/unlockre/keycomps/compare/v1.24.0...v1.25.0) (2025-02-27)


### Bug Fixes

* Fix no PropertyReviewSummary error ([#565](https://github.com/unlockre/keycomps/issues/565)) ([02f9a67](https://github.com/unlockre/keycomps/commit/02f9a6782646fe601f53e6426ce392ae8c57f880))
* Update @unlockre/graph-tools dependency ([#561](https://github.com/unlockre/keycomps/issues/561)) ([e032fc3](https://github.com/unlockre/keycomps/commit/e032fc320ad2b5b95d962bb0d2416153b42a778c))


### Features

* Add filter to Estimated Unit Mix table ([#548](https://github.com/unlockre/keycomps/issues/548)) ([599c355](https://github.com/unlockre/keycomps/commit/599c3557abb864304f18b3a487db5a1080949d5b))
* Add missing listing status filters ([#559](https://github.com/unlockre/keycomps/issues/559)) ([4456c81](https://github.com/unlockre/keycomps/commit/4456c81d4ada7daea5106919ce1630ef55948999))
* Add propertyReviewSummaryCompColumn logic ([#552](https://github.com/unlockre/keycomps/issues/552)) ([f53408a](https://github.com/unlockre/keycomps/commit/f53408a0493697a2a78384ed46bb4dfcf2bc53db))
* Update @unlockre/components-library dependency ([#556](https://github.com/unlockre/keycomps/issues/556)) ([dd83856](https://github.com/unlockre/keycomps/commit/dd838568f9de827720edd18a144e7a329d162734))

# [1.24.0](https://github.com/unlockre/keycomps/compare/v1.23.1...v1.24.0) (2025-02-27)


### Bug Fixes

* Add sixBedroom UnitType ([#553](https://github.com/unlockre/keycomps/issues/553)) ([c57ddb6](https://github.com/unlockre/keycomps/commit/c57ddb66bb07c7ea777f787c764fc677ebae59ed))
* Fix Rent Insights tab print view ([#557](https://github.com/unlockre/keycomps/issues/557)) ([600489e](https://github.com/unlockre/keycomps/commit/600489eceec68272d06a14b4f26ec36a7b1fa2c1))
* Fix updateCellRange fn signature ([#542](https://github.com/unlockre/keycomps/issues/542)) ([9ba1a38](https://github.com/unlockre/keycomps/commit/9ba1a38e63c83204f0c5c4488cf325eebf2106aa))
* Rename rent functions and vars ([#545](https://github.com/unlockre/keycomps/issues/545)) ([15b6a8e](https://github.com/unlockre/keycomps/commit/15b6a8ecf8ea2b3b57327ba80a617f96547d18ed))
* Show CancelEditionDialog on back ([#554](https://github.com/unlockre/keycomps/issues/554)) ([275f982](https://github.com/unlockre/keycomps/commit/275f98299e750a2a6618735f4657bc783bc057d3))


### Features

* Add date title on historical graph tooltip ([#544](https://github.com/unlockre/keycomps/issues/544)) ([23a725b](https://github.com/unlockre/keycomps/commit/23a725be73af154029ffe92410a4dd826b0829e0))
* Add PropertyReviewSummary and CompSetReviewsProcess logic ([#551](https://github.com/unlockre/keycomps/issues/551)) ([437dd88](https://github.com/unlockre/keycomps/commit/437dd88356faea41c7e7c6a0a06ea484df086737))
* Add with rent data filter in suggestion and edit other comps ([#539](https://github.com/unlockre/keycomps/issues/539)) ([e637e60](https://github.com/unlockre/keycomps/commit/e637e604649261e7b68a499c5fef992de973db1e))
* Show an AlertDialog on compSet cancel edition ([#550](https://github.com/unlockre/keycomps/issues/550)) ([3e200cc](https://github.com/unlockre/keycomps/commit/3e200cc02427b2fe41ee52a00f19dc7167a88c6c))
* Update @unlockre/components-library dependency ([#549](https://github.com/unlockre/keycomps/issues/549)) ([bd67223](https://github.com/unlockre/keycomps/commit/bd67223ae5b0433ccbb568d2376a55a17749a8a8))

## [1.23.1](https://github.com/unlockre/keycomps/compare/v1.23.0...v1.23.1) (2025-02-10)


### Bug Fixes

* Fix ratio formatting ([#546](https://github.com/unlockre/keycomps/issues/546)) ([1e2ebd2](https://github.com/unlockre/keycomps/commit/1e2ebd24497da21ef4f39a0788455f0abc8586f9))

# [1.23.0](https://github.com/unlockre/keycomps/compare/v1.22.0...v1.23.0) (2025-02-06)


### Bug Fixes

* Update rent insights tab with browser navigation ([#538](https://github.com/unlockre/keycomps/issues/538)) ([383f910](https://github.com/unlockre/keycomps/commit/383f910d2241e613c8ce79599bef3f1ca5101f02))


### Features

* Add concession percentage column ([#524](https://github.com/unlockre/keycomps/issues/524)) ([6aba092](https://github.com/unlockre/keycomps/commit/6aba092c76630580c630ed46747a5ff4d0de5917))
* Add new changes to Artemis excel ([#540](https://github.com/unlockre/keycomps/issues/540)) ([cc0ceae](https://github.com/unlockre/keycomps/commit/cc0ceaec0f58bcd8ac5212cada6d944d3a9bd2c6))
* Control rent insights tabs via url ([#537](https://github.com/unlockre/keycomps/issues/537)) ([a891690](https://github.com/unlockre/keycomps/commit/a891690c0e76abcff6f4fdabc069b465ccf7ced1))
* Update @unlockre/utils-amplitude dependency ([#536](https://github.com/unlockre/keycomps/issues/536)) ([78a2dfa](https://github.com/unlockre/keycomps/commit/78a2dfaaf688ebf158eef9366b1b0541f7524dc6))
* Use latest VirtualTable ([#528](https://github.com/unlockre/keycomps/issues/528)) ([2d8e144](https://github.com/unlockre/keycomps/commit/2d8e1449e8b67e372d00509526e4869dbc12bc6b))

# [1.22.0](https://github.com/unlockre/keycomps/compare/v1.21.2...v1.22.0) (2025-01-29)


### Bug Fixes

* Remove CompsTable sortable columns ([#533](https://github.com/unlockre/keycomps/issues/533)) ([b35a3da](https://github.com/unlockre/keycomps/commit/b35a3dacc3b0fea290aa9a9a63357e8210c6e970))
* Remove CompsTable sorting ([#532](https://github.com/unlockre/keycomps/issues/532)) ([b41a1ff](https://github.com/unlockre/keycomps/commit/b41a1ff931807e1481df4819f7a1f9504b0f76c7))
* Support no HistoricalRentSummary ([#527](https://github.com/unlockre/keycomps/issues/527)) ([c57a8ae](https://github.com/unlockre/keycomps/commit/c57a8ae762867552146dcf2902210adf54f51b23))
* Use median square footage ([#522](https://github.com/unlockre/keycomps/issues/522)) ([85bfb23](https://github.com/unlockre/keycomps/commit/85bfb236e228b2e2e0c1593bbccf796076610302))


### Features

* Add ByFloorPlanTab ([#505](https://github.com/unlockre/keycomps/issues/505)) ([cf986af](https://github.com/unlockre/keycomps/commit/cf986af346b7f6da6e396195597d33b986876d42))
* Add FloorPlanTable ([#520](https://github.com/unlockre/keycomps/issues/520)) ([442a163](https://github.com/unlockre/keycomps/commit/442a1638abc8852b1feae5d181a0136f581c8753))
* Support comps reordering ([#523](https://github.com/unlockre/keycomps/issues/523)) ([4308ed9](https://github.com/unlockre/keycomps/commit/4308ed91096014ae855eb8c90e77fac8a0e13685))
* Update @unlockre/components-library dependency ([#521](https://github.com/unlockre/keycomps/issues/521)) ([0a63724](https://github.com/unlockre/keycomps/commit/0a63724072693f43c3040512c96cbceeda671447))

## [1.21.2](https://github.com/unlockre/keycomps/compare/v1.21.1...v1.21.2) (2025-01-21)


### Bug Fixes

* Update listings date range ([#518](https://github.com/unlockre/keycomps/issues/518)) ([89f57fe](https://github.com/unlockre/keycomps/commit/89f57febff08cba4d7664c7da79bbd2ab8968b9d))

## [1.21.1](https://github.com/unlockre/keycomps/compare/v1.21.0...v1.21.1) (2025-01-20)


### Bug Fixes

* Change rent suggestion date range ([#516](https://github.com/unlockre/keycomps/issues/516)) ([3674674](https://github.com/unlockre/keycomps/commit/3674674dc52a247f7cb08fda8652be825b2bcea8))

# [1.21.0](https://github.com/unlockre/keycomps/compare/v1.20.0...v1.21.0) (2025-01-17)


### Bug Fixes

* Fix home/searchCompSets event tracking ([#513](https://github.com/unlockre/keycomps/issues/513)) ([45e32c1](https://github.com/unlockre/keycomps/commit/45e32c1d1086a881a27b49cf68e93e6d70a38a86))
* Handle no unit rent suggestions case ([#510](https://github.com/unlockre/keycomps/issues/510)) ([e8db8a6](https://github.com/unlockre/keycomps/commit/e8db8a6100f0df693e2bd9d2b7f8481d5b4410d5))
* Handle rent api 404 response ([#508](https://github.com/unlockre/keycomps/issues/508)) ([be2537a](https://github.com/unlockre/keycomps/commit/be2537a7f6c5e5351e40bd70e0a367da35cfedae))


### Features

* Align screens with designs ([#511](https://github.com/unlockre/keycomps/issues/511)) ([fc2dcb8](https://github.com/unlockre/keycomps/commit/fc2dcb82d7e95f7002a9c07fd3f77bc6d0cb83af))
* Update rent suggestion date range ([#501](https://github.com/unlockre/keycomps/issues/501)) ([4203832](https://github.com/unlockre/keycomps/commit/4203832d7c28f397c9544c9c19eda6d0f14c3dff))

# [1.20.0](https://github.com/unlockre/keycomps/compare/v1.19.0...v1.20.0) (2025-01-16)


### Features

* Add exposure comp column ([#506](https://github.com/unlockre/keycomps/issues/506)) ([80229a5](https://github.com/unlockre/keycomps/commit/80229a5a9f548224fe5ec3f262f926d9ea41be45))

# [1.19.0](https://github.com/unlockre/keycomps/compare/v1.18.0...v1.19.0) (2025-01-14)


### Bug Fixes

* Change concessions details column position in artemis excel ([#498](https://github.com/unlockre/keycomps/issues/498)) ([b44569e](https://github.com/unlockre/keycomps/commit/b44569e537b1f795864eaf56c5a3a08a18a03407))
* Make comp set link work properly ([#483](https://github.com/unlockre/keycomps/issues/483)) ([2e4adc4](https://github.com/unlockre/keycomps/commit/2e4adc48a0fb63eee42f1fd5fedd825e23205c2b))


### Features

* Add button to copy property id ([#484](https://github.com/unlockre/keycomps/issues/484)) ([2092b51](https://github.com/unlockre/keycomps/commit/2092b51f071eb4366a20701b696939ea37d4d902))
* Add CopyButton ([#497](https://github.com/unlockre/keycomps/issues/497)) ([8e20296](https://github.com/unlockre/keycomps/commit/8e202960d63d2af370b7b89416e632788ec3f4bf))
* Add page titles ([#482](https://github.com/unlockre/keycomps/issues/482)) ([f700550](https://github.com/unlockre/keycomps/commit/f7005502ffcbadde69488f7b3a4752e6eb44a6da))
* Return only comps unitRentSuggestions with rent ([#495](https://github.com/unlockre/keycomps/issues/495)) ([e734154](https://github.com/unlockre/keycomps/commit/e7341546f510f520058c834719b33d59cd18a8ab))
* Split artemis excel in different sheets ([#486](https://github.com/unlockre/keycomps/issues/486)) ([8f4c214](https://github.com/unlockre/keycomps/commit/8f4c21443117ffc15d0e3cbc67b627d52ac36498))
* Track adding multiple comps events ([#493](https://github.com/unlockre/keycomps/issues/493)) ([44339c3](https://github.com/unlockre/keycomps/commit/44339c3bd47a9d5332e43b165b856b389b5fd497))
* Update AddMultipleCompsModal copy ([#496](https://github.com/unlockre/keycomps/issues/496)) ([3ef7c23](https://github.com/unlockre/keycomps/commit/3ef7c231aae65421e563c2bb86ac2aeb4a38a565))

# [1.18.0](https://github.com/unlockre/keycomps/compare/v1.17.0...v1.18.0) (2025-01-09)


### Bug Fixes

* Discard properties with more than 1 match ([#489](https://github.com/unlockre/keycomps/issues/489)) ([0062a95](https://github.com/unlockre/keycomps/commit/0062a95cffa9c4ed6700e6043f4b991af6a80461))
* Fix NaN in source stats when showing average and there's no data ([#492](https://github.com/unlockre/keycomps/issues/492)) ([53872c8](https://github.com/unlockre/keycomps/commit/53872c8929b0fac4d383d1c1f5af2a41640da324))
* Increase matching to avoid false porsitives ([#488](https://github.com/unlockre/keycomps/issues/488)) ([913bfba](https://github.com/unlockre/keycomps/commit/913bfbae4f766eea417d5f02b4fac6bd8f9fbf85))
* Prevent duplicated selected comps ([#487](https://github.com/unlockre/keycomps/issues/487)) ([7cd5cbc](https://github.com/unlockre/keycomps/commit/7cd5cbc626a61cfe1f1f8b59c1d5a094a326f2db))


### Features

* Add allowAddingMultipleProperties feature flag ([#490](https://github.com/unlockre/keycomps/issues/490)) ([67ef56f](https://github.com/unlockre/keycomps/commit/67ef56f9fc4f2fe88de245b5aa7eaaed4cf4d5d5))
* Add compset modal all sources mode ([#477](https://github.com/unlockre/keycomps/issues/477)) ([6e86e4d](https://github.com/unlockre/keycomps/commit/6e86e4d8eccc07f4e96c9e22efb344746fc700c3))
* Add Multiple Comps BFF logic ([#475](https://github.com/unlockre/keycomps/issues/475)) ([7f38bba](https://github.com/unlockre/keycomps/commit/7f38bba60954f4f5e3dcde6138f331955f5b893f))
* Add rating to source select ([#476](https://github.com/unlockre/keycomps/issues/476)) ([f59e689](https://github.com/unlockre/keycomps/commit/f59e6895cf4346100f37a1ed5c353c247c3adf15))
* Show rating cards based on selected source ([#480](https://github.com/unlockre/keycomps/issues/480)) ([489c6a3](https://github.com/unlockre/keycomps/commit/489c6a34d7224d8c9f529d143471bd8ef6af6b04))

# [1.17.0](https://github.com/unlockre/keycomps/compare/v1.16.1...v1.17.0) (2025-01-06)


### Bug Fixes

* Correct scroll on multi source stats ([#470](https://github.com/unlockre/keycomps/issues/470)) ([98fb0ff](https://github.com/unlockre/keycomps/commit/98fb0ff28ac2004e4ec40fb7ecf65e466bbd2133))
* Improve error message handling scenarios ([#366](https://github.com/unlockre/keycomps/issues/366)) ([5f11ef7](https://github.com/unlockre/keycomps/commit/5f11ef72b9fc8ece43d2754b5237845dac4e06df))
* Prevent eternal loading state ([#465](https://github.com/unlockre/keycomps/issues/465)) ([1c9a49d](https://github.com/unlockre/keycomps/commit/1c9a49d4f88112608509d9ce7cd721e65ec4ba6f))
* Replace underscore in quality category names ([#466](https://github.com/unlockre/keycomps/issues/466)) ([e3dbd0e](https://github.com/unlockre/keycomps/commit/e3dbd0e3baeb4b655bcd097cd52f680194c56d9e))
* Show all concessions ([#463](https://github.com/unlockre/keycomps/issues/463)) ([d137ab6](https://github.com/unlockre/keycomps/commit/d137ab66924face9735ae90140d38619a363ce67))
* Show all concessions if no active ones ([#479](https://github.com/unlockre/keycomps/issues/479)) ([2daebb5](https://github.com/unlockre/keycomps/commit/2daebb5f4de5523143f7fe0b442e9d169c3ea3b3))
* Show right rent difference on tooltip ([#457](https://github.com/unlockre/keycomps/issues/457)) ([7191bd1](https://github.com/unlockre/keycomps/commit/7191bd12b639325bebf70091b69d26c4a8e84d9e))


### Features

* Add AddMultipleCompsButton ([#459](https://github.com/unlockre/keycomps/issues/459)) ([5c36876](https://github.com/unlockre/keycomps/commit/5c36876413bc870ba38e089441341beb2983baef))
* Add AddMultipleCompsModal ([#460](https://github.com/unlockre/keycomps/issues/460)) ([7077ee2](https://github.com/unlockre/keycomps/commit/7077ee25028edfd6afa241218f2702cb354bb244))
* Add filtering styling for compset modal ([#469](https://github.com/unlockre/keycomps/issues/469)) ([97bb926](https://github.com/unlockre/keycomps/commit/97bb9261373361c675b6e8429805e472776163c1))
* Add PricingRecommendationsSection ([#447](https://github.com/unlockre/keycomps/issues/447)) ([4c42b1e](https://github.com/unlockre/keycomps/commit/4c42b1e60791f84d24ead0546856340d1eed2384)), closes [#448](https://github.com/unlockre/keycomps/issues/448)
* Add PropertyQualityDetails events ([#449](https://github.com/unlockre/keycomps/issues/449)) ([5cdde65](https://github.com/unlockre/keycomps/commit/5cdde6545bb7990ef62a6f2cf9f8ac10c6665b42))
* Add RecommendedRentDetailsModal ([#451](https://github.com/unlockre/keycomps/issues/451)) ([3c54a39](https://github.com/unlockre/keycomps/commit/3c54a397203f5fcb7fe89fb99a65749563c4f406))
* Add reviews performance source filter ([#467](https://github.com/unlockre/keycomps/issues/467)) ([9d1d897](https://github.com/unlockre/keycomps/commit/9d1d897787d029987af7969baefadfcc486d2bb1))
* Add SimilarUnitsSection ([#458](https://github.com/unlockre/keycomps/issues/458)) ([af2e5b5](https://github.com/unlockre/keycomps/commit/af2e5b5ac6722b40784cc807326c6bdd14856dd8))
* Add source filtering ([#471](https://github.com/unlockre/keycomps/issues/471)) ([03e707b](https://github.com/unlockre/keycomps/commit/03e707b77187217ec20c74b8146842428f88a949))
* Add source select ([#468](https://github.com/unlockre/keycomps/issues/468)) ([c02d75b](https://github.com/unlockre/keycomps/commit/c02d75b80978ccf437f79f7d16b1020dc6fdbc55))
* Add UnitComparisonItem logic ([#461](https://github.com/unlockre/keycomps/issues/461)) ([6f35927](https://github.com/unlockre/keycomps/commit/6f35927e4ed07947b0efecdf7cf042522e8d00ec))
* Add UnitRentSuggestion logic ([#448](https://github.com/unlockre/keycomps/issues/448)) ([a6c6070](https://github.com/unlockre/keycomps/commit/a6c607033f55d3f659d9b4bce853757c8a519d5b))
* Display no values compset review rows as hyphens ([#473](https://github.com/unlockre/keycomps/issues/473)) ([f8a0a97](https://github.com/unlockre/keycomps/commit/f8a0a9788045ac9c65a9a50f96a2ceb251366e81))
* Integrate RecommendedRentDetailsModal ([#455](https://github.com/unlockre/keycomps/issues/455)) ([314dc60](https://github.com/unlockre/keycomps/commit/314dc6053d9b1df76d79cb634d56cf10dfc849bb))
* Make Unit.bathrooms field mandatory ([#452](https://github.com/unlockre/keycomps/issues/452)) ([dec4792](https://github.com/unlockre/keycomps/commit/dec4792574591df2f2b88a95709df5fb0d7a4d3e))
* Show active concessions by default ([#478](https://github.com/unlockre/keycomps/issues/478)) ([9a6ebaf](https://github.com/unlockre/keycomps/commit/9a6ebaf49f84e3203a6034d3c674580b7f9f7c5d))
* Support units with five bedrooms ([#456](https://github.com/unlockre/keycomps/issues/456)) ([4cf2534](https://github.com/unlockre/keycomps/commit/4cf25343c29926fb40817fb6a929bc7e4b972428))

## [1.16.1](https://github.com/unlockre/keycomps/compare/v1.16.0...v1.16.1) (2024-12-12)


### Bug Fixes

* Fix malformed property zip code ([#453](https://github.com/unlockre/keycomps/issues/453)) ([a3c1846](https://github.com/unlockre/keycomps/commit/a3c18469062f2f1f3763eeb4df2cfe1e556c3d80))

# [1.16.0](https://github.com/unlockre/keycomps/compare/v1.15.1...v1.16.0) (2024-12-05)


### Bug Fixes

* Fix ListingTable not showing renovated column ([#444](https://github.com/unlockre/keycomps/issues/444)) ([46589cc](https://github.com/unlockre/keycomps/commit/46589cc478e0a250f9e9a2f61d284a9c8a9bc364))
* Hide PricingRecommendationsTab ([#439](https://github.com/unlockre/keycomps/issues/439)) ([90c32d5](https://github.com/unlockre/keycomps/commit/90c32d5c2c6e1e6937d9496865a305339b23c28b))
* Remove old artemis excel exporter ([#405](https://github.com/unlockre/keycomps/issues/405)) ([be3880f](https://github.com/unlockre/keycomps/commit/be3880fdc6a1ee9b69ec14cb7271a216f359fe0f))
* Show Quality Score ([#441](https://github.com/unlockre/keycomps/issues/441)) ([09b99c0](https://github.com/unlockre/keycomps/commit/09b99c0d42d04d4c82b7eed65aac58f7b78af818))


### Features

* Add PricingRecommendationsTab to Rent Insights ([#434](https://github.com/unlockre/keycomps/issues/434)) ([4c4d285](https://github.com/unlockre/keycomps/commit/4c4d2859aa980a1b170a17752e50571ab51c75e9))
* Add Property Quality column to CompsTable ([#417](https://github.com/unlockre/keycomps/issues/417)) ([58d4b78](https://github.com/unlockre/keycomps/commit/58d4b78156005d42294da472a729f7be27659ffe))
* Add Property Quality to CompPropertyDrawer ([#421](https://github.com/unlockre/keycomps/issues/421)) ([8493636](https://github.com/unlockre/keycomps/commit/84936368553ec1cefa0fca0a42e52a9f11fef428))
* Add quality to similarity score ([#429](https://github.com/unlockre/keycomps/issues/429)) ([69cd8b7](https://github.com/unlockre/keycomps/commit/69cd8b71b683f0cb1fdec8751fbf1f2f062d2ca2))
* Add test id for tenants sentiments widget ([#440](https://github.com/unlockre/keycomps/issues/440)) ([5c90571](https://github.com/unlockre/keycomps/commit/5c905714588b3ae718bf6d2beba234b29e3a31dd))
* Add UnitRenovationStatus filtering ([#442](https://github.com/unlockre/keycomps/issues/442)) ([34dca70](https://github.com/unlockre/keycomps/commit/34dca70bfb58829d89fd5ec347106a36e1be58f3))

## [1.15.1](https://github.com/unlockre/keycomps/compare/v1.15.0...v1.15.1) (2024-12-04)


### Bug Fixes

* Fix UnitConfig sorting ([#436](https://github.com/unlockre/keycomps/issues/436)) ([0cef905](https://github.com/unlockre/keycomps/commit/0cef9051285fc836dc1c5793af4ae6c8e00c85e1))

# [1.15.0](https://github.com/unlockre/keycomps/compare/v1.14.1...v1.15.0) (2024-12-03)


### Features

* Change google reviews label to average  ([#432](https://github.com/unlockre/keycomps/issues/432)) ([325b432](https://github.com/unlockre/keycomps/commit/325b4320432f0e63aa5fc12e42b083191427e9fd)), closes [#430](https://github.com/unlockre/keycomps/issues/430)

## [1.14.1](https://github.com/unlockre/keycomps/compare/v1.14.0...v1.14.1) (2024-12-02)


### Bug Fixes

* Simplify renovationProbability logic ([#427](https://github.com/unlockre/keycomps/issues/427)) ([d37cc70](https://github.com/unlockre/keycomps/commit/d37cc70f01d4e58d2cbdb63c4b4c0cbd85c96582))

# [1.14.0](https://github.com/unlockre/keycomps/compare/v1.13.0...v1.14.0) (2024-11-29)


### Bug Fixes

* Fix renovationProbability logic ([#423](https://github.com/unlockre/keycomps/issues/423)) ([0f3a007](https://github.com/unlockre/keycomps/commit/0f3a007fd0dab72842170d63706694ec7b652463))
* Fix renovationProbability logic (round [#2](https://github.com/unlockre/keycomps/issues/2)) ([#425](https://github.com/unlockre/keycomps/issues/425)) ([78634b4](https://github.com/unlockre/keycomps/commit/78634b40974fd06783dd746aebcb13e74a1da8d1))
* Handle 404 on PropertyQualitySummary relation fetching ([#418](https://github.com/unlockre/keycomps/issues/418)) ([8a3be6a](https://github.com/unlockre/keycomps/commit/8a3be6a737286c9357986ca510d42768e838abbc))
* Move getPropertyQualitySummary error handling ([#419](https://github.com/unlockre/keycomps/issues/419)) ([ee8bb4d](https://github.com/unlockre/keycomps/commit/ee8bb4d48025c838cae81bcf9bb7968982801835))


### Features

* Add PropertyQualitySummary logic ([#415](https://github.com/unlockre/keycomps/issues/415)) ([860a706](https://github.com/unlockre/keycomps/commit/860a706671dfb7d190652c947fe68397daa1bad0))
* Add renovation columns to UnitMixTable ([#404](https://github.com/unlockre/keycomps/issues/404)) ([d5ec96f](https://github.com/unlockre/keycomps/commit/d5ec96f62ff45ab1c7b94e2ae7aa8bfd194ad0d9))
* Update texts ([#416](https://github.com/unlockre/keycomps/issues/416)) ([4934cdf](https://github.com/unlockre/keycomps/commit/4934cdf9352c07717d9823603f0d3e6b6adbf293))

# [1.13.0](https://github.com/unlockre/keycomps/compare/v1.12.0...v1.13.0) (2024-11-25)


### Bug Fixes

* Change benefit type and kind columns order ([#408](https://github.com/unlockre/keycomps/issues/408)) ([d98586c](https://github.com/unlockre/keycomps/commit/d98586c9731150230b78cf3a0f5a4efb68d6d077))
* Show property rent in RentInsightsTable ([#411](https://github.com/unlockre/keycomps/issues/411)) ([e58e996](https://github.com/unlockre/keycomps/commit/e58e9962293a31904b16c24549db036935852aab))


### Features

* Add tooltip to renovation rent info ([#409](https://github.com/unlockre/keycomps/issues/409)) ([50c74b0](https://github.com/unlockre/keycomps/commit/50c74b0ae9aebc2b43d831713d065851d4178882))

# [1.12.0](https://github.com/unlockre/keycomps/compare/v1.11.2...v1.12.0) (2024-11-22)


### Bug Fixes

* Add units relation to getProperty ([#401](https://github.com/unlockre/keycomps/issues/401)) ([bb735ed](https://github.com/unlockre/keycomps/commit/bb735ed2d4591c33724580104f825af8ee5e5dd8))
* Update keyos-tools dependency ([#406](https://github.com/unlockre/keycomps/issues/406)) ([6aa494b](https://github.com/unlockre/keycomps/commit/6aa494bb7cd6a96844d5a0e9a543c58e947a7f8a))


### Features

* Add renovation info in by-unit-type tab ([#388](https://github.com/unlockre/keycomps/issues/388)) ([256a616](https://github.com/unlockre/keycomps/commit/256a616c9dd04c083110e90eba1b231a8f924997))
* Add unknown state to RenovatedUnitPill ([#402](https://github.com/unlockre/keycomps/issues/402)) ([a9ff656](https://github.com/unlockre/keycomps/commit/a9ff656a02645481a1baaf36d1643d29b56edb60))

## [1.11.2](https://github.com/unlockre/keycomps/compare/v1.11.1...v1.11.2) (2024-11-20)


### Bug Fixes

* Use weighted averages for property-level rents ([#391](https://github.com/unlockre/keycomps/issues/391)) ([68bde47](https://github.com/unlockre/keycomps/commit/68bde47763e4f4e59404318fd4dddd7c24da1887))

## [1.11.1](https://github.com/unlockre/keycomps/compare/v1.11.0...v1.11.1) (2024-11-17)


### Bug Fixes

* Update @unlockre/components-library dependency ([#389](https://github.com/unlockre/keycomps/issues/389)) ([2b3c2ea](https://github.com/unlockre/keycomps/commit/2b3c2ead00d8b7548155bd23e422b33f49ef7e19))

# [1.11.0](https://github.com/unlockre/keycomps/compare/v1.10.0...v1.11.0) (2024-11-15)


### Features

* Add new sheet in artemis excel ([#382](https://github.com/unlockre/keycomps/issues/382)) ([aa97e46](https://github.com/unlockre/keycomps/commit/aa97e46b1e58d89f3d5c6739dbce32db6b0a3990))
* Track CompSetViewScreen events ([#383](https://github.com/unlockre/keycomps/issues/383)) ([adaade8](https://github.com/unlockre/keycomps/commit/adaade8201a045b6784a63afd99aea2e33184fd5))
* Track Home and Comp Set Edit screens events ([#377](https://github.com/unlockre/keycomps/issues/377)) ([590450c](https://github.com/unlockre/keycomps/commit/590450cd262b3997c4c8815102b36235ff372e4e))
* Track Rent Insights By Unit Type table scroll event ([#385](https://github.com/unlockre/keycomps/issues/385)) ([32d4104](https://github.com/unlockre/keycomps/commit/32d410499bbaebe16c3a734805c5eb4ccd05b116))

# [1.10.0](https://github.com/unlockre/keycomps/compare/v1.9.0...v1.10.0) (2024-11-13)


### Bug Fixes

* Remove formatting from Concessions Value column ([#381](https://github.com/unlockre/keycomps/issues/381)) ([6529e06](https://github.com/unlockre/keycomps/commit/6529e06cea08667f0575a512bd5721e632e5231f))


### Features

* Add columns to ConcessionTable ([#376](https://github.com/unlockre/keycomps/issues/376)) ([92d9586](https://github.com/unlockre/keycomps/commit/92d95860b65221258c582dab6c46661fa280eea8))

# [1.9.0](https://github.com/unlockre/keycomps/compare/v1.8.1...v1.9.0) (2024-11-06)


### Bug Fixes

* Export Concessions, Fees, and Comps tables ([#372](https://github.com/unlockre/keycomps/issues/372)) ([3830d5d](https://github.com/unlockre/keycomps/commit/3830d5dbe44cc6ea282ce43954e3103a481e6158))
* Restore feature flag in Listings CSV ([#370](https://github.com/unlockre/keycomps/issues/370)) ([83a5dce](https://github.com/unlockre/keycomps/commit/83a5dce2e79835291a90ae976ec3789f47b4970a))


### Features

* Add estimated occupancy to full info table ([#369](https://github.com/unlockre/keycomps/issues/369)) ([249fe4c](https://github.com/unlockre/keycomps/commit/249fe4c660e20db8ebb5048854d2bb358f9da1da))
* Add Reno column to Listing and Unit tables ([#365](https://github.com/unlockre/keycomps/issues/365)) ([75f7ff2](https://github.com/unlockre/keycomps/commit/75f7ff22aaed58e406d1a71d9c4a558c0e3a7800))
* Order summaries by descending date + Add rating data-testid ([#373](https://github.com/unlockre/keycomps/issues/373)) ([accddd7](https://github.com/unlockre/keycomps/commit/accddd7c68b14e58ea7029a2694f4708625550c7))

## [1.8.1](https://github.com/unlockre/keycomps/compare/v1.8.0...v1.8.1) (2024-10-30)


### Bug Fixes

* Sort bedrooms in Unit Type select ([#362](https://github.com/unlockre/keycomps/issues/362)) ([68334ba](https://github.com/unlockre/keycomps/commit/68334bae22292e2ab3948a7c23c6640589069db3))

# [1.8.0](https://github.com/unlockre/keycomps/compare/v1.7.1...v1.8.0) (2024-10-30)


### Bug Fixes

* Hide UnitMixTable summary row if no data ([#355](https://github.com/unlockre/keycomps/issues/355)) ([f748583](https://github.com/unlockre/keycomps/commit/f74858314e47b1ccf74f8d654415a5ba5a35f53a))
* Hide UnitTable summary row if no data ([#356](https://github.com/unlockre/keycomps/issues/356)) ([227a2a8](https://github.com/unlockre/keycomps/commit/227a2a8abaaf463668f82a456d222fb45c3574e0))
* Update Unit Mix Confidence column title ([#354](https://github.com/unlockre/keycomps/issues/354)) ([806bbaa](https://github.com/unlockre/keycomps/commit/806bbaa4ec4e0a295b1932c31bba129a8e20303d))


### Features

* Add by unit types ([#346](https://github.com/unlockre/keycomps/issues/346)) ([62b24b4](https://github.com/unlockre/keycomps/commit/62b24b41c0ccfebe1651a72f071741975ec66ec1))
* Add Export Table to UnitMixTable ([#353](https://github.com/unlockre/keycomps/issues/353)) ([a093fd3](https://github.com/unlockre/keycomps/commit/a093fd33a6ecc2140ca6e38f98e90f29fbc45033))
* Add listings table unit config filter ([#357](https://github.com/unlockre/keycomps/issues/357)) ([5e084af](https://github.com/unlockre/keycomps/commit/5e084af5ec3a96213773cc24466dd604cc116998))
* Add PercentagePill to UnitMixTable ([#348](https://github.com/unlockre/keycomps/issues/348)) ([fca4ac6](https://github.com/unlockre/keycomps/commit/fca4ac622cd1f8d587dd7a1908c7d456f5398f21))
* Add rent insights tabs ([#345](https://github.com/unlockre/keycomps/issues/345)) ([195faab](https://github.com/unlockre/keycomps/commit/195faab1e9b4b6e3b41aad1ff34625f8079a285a))
* Add unit tab ([#349](https://github.com/unlockre/keycomps/issues/349)) ([de5dcc3](https://github.com/unlockre/keycomps/commit/de5dcc3209159bde33152b9bc1cf2ab559bd9004))
* Add unit table unit config filter ([#359](https://github.com/unlockre/keycomps/issues/359)) ([0892a19](https://github.com/unlockre/keycomps/commit/0892a19ac26a63049f4022cdb1cb52df79ef49c8))
* Add UnitMixTable ([#347](https://github.com/unlockre/keycomps/issues/347)) ([4ff9cc1](https://github.com/unlockre/keycomps/commit/4ff9cc162ec2b86062402fa63ee5e808b8ca6322))

## [1.7.1](https://github.com/unlockre/keycomps/compare/v1.7.0...v1.7.1) (2024-10-29)


### Bug Fixes

* Replace permissions validation with ff in reviews ([#351](https://github.com/unlockre/keycomps/issues/351)) ([c2e200f](https://github.com/unlockre/keycomps/commit/c2e200f2c6c2429424250bc50139c2a43f4fc9d5))

# [1.7.0](https://github.com/unlockre/keycomps/compare/v1.6.2...v1.7.0) (2024-10-24)


### Bug Fixes

* Clear getPropertyComps query cache on unmount ([#342](https://github.com/unlockre/keycomps/issues/342)) ([73a41ae](https://github.com/unlockre/keycomps/commit/73a41ae878e060a97bf9e15152da53e7443b2c50))
* Correct reviews not working on reload ([#338](https://github.com/unlockre/keycomps/issues/338)) ([6d1ba50](https://github.com/unlockre/keycomps/commit/6d1ba5070e5c95c972bbab75793f8805f480257e))
* Correct wrong extrapolation on reviews chart ([#339](https://github.com/unlockre/keycomps/issues/339)) ([d7412b6](https://github.com/unlockre/keycomps/commit/d7412b69b72a71e3b161cd5643b77d0d2c4ccf04))
* Handle 404 errors ([#335](https://github.com/unlockre/keycomps/issues/335)) ([a1d8a41](https://github.com/unlockre/keycomps/commit/a1d8a41d5723ffb462ab0a053f4555bd4a37b317))
* Other comps pagination ([#340](https://github.com/unlockre/keycomps/issues/340)) ([d14af13](https://github.com/unlockre/keycomps/commit/d14af13b19498aae04bcedd769dbde8436dbde42))


### Features

* Add min and max rent comp columns ([#334](https://github.com/unlockre/keycomps/issues/334)) ([385eec0](https://github.com/unlockre/keycomps/commit/385eec070a6a73834a751b04892d7b0005344262))
* Add multisource link support ([#333](https://github.com/unlockre/keycomps/issues/333)) ([e0d2e53](https://github.com/unlockre/keycomps/commit/e0d2e53b97c225682b0de74cb6f105bd30bc818b))
* Improve responsiveness for reviews chart ([#319](https://github.com/unlockre/keycomps/issues/319)) ([0bd5159](https://github.com/unlockre/keycomps/commit/0bd5159cfdffa28841f564cd25ac0ec7b950f2ec))
* Limit access to keyreviews by permission ([#331](https://github.com/unlockre/keycomps/issues/331)) ([698412e](https://github.com/unlockre/keycomps/commit/698412e075059d4e59dab5f7d515ca94eb067653))

## [1.6.2](https://github.com/unlockre/keycomps/compare/v1.6.1...v1.6.2) (2024-10-18)


### Bug Fixes

* Add export tabs separator in artemis excel ([#330](https://github.com/unlockre/keycomps/issues/330)) ([4d5ab55](https://github.com/unlockre/keycomps/commit/4d5ab55262cfb9d8ec40806e480cc098bf4b296a))

## [1.6.1](https://github.com/unlockre/keycomps/compare/v1.6.0...v1.6.1) (2024-10-18)


### Bug Fixes

* Remove useClickOutisde from date range selector ([#328](https://github.com/unlockre/keycomps/issues/328)) ([41490fa](https://github.com/unlockre/keycomps/commit/41490fac4842f11dd113cf9f873113a32df474fd))

# [1.6.0](https://github.com/unlockre/keycomps/compare/v1.5.0...v1.6.0) (2024-10-18)


### Bug Fixes

* Fix markdown styles ([#313](https://github.com/unlockre/keycomps/issues/313)) ([7bf5f36](https://github.com/unlockre/keycomps/commit/7bf5f36eeac29db73a6c69c633f0e46988c791d2))
* Fix modal backdrop scroll ([#306](https://github.com/unlockre/keycomps/issues/306)) ([cae81a9](https://github.com/unlockre/keycomps/commit/cae81a9bb324b2f8558503807ba40f13d7a30d65))
* Format on new artemis table ([#325](https://github.com/unlockre/keycomps/issues/325)) ([0181f6d](https://github.com/unlockre/keycomps/commit/0181f6d29dc871273553ebcaa35a01a11494ef11))
* Handle undefined unit rent group in artemis excel ([#323](https://github.com/unlockre/keycomps/issues/323)) ([5527ffc](https://github.com/unlockre/keycomps/commit/5527ffc61e840318fb6a9792c126a9bbd48c5f3c))
* Hide empty tooltip in base card ([#310](https://github.com/unlockre/keycomps/issues/310)) ([529f117](https://github.com/unlockre/keycomps/commit/529f117e591f860fb464c0d497223bcfa204e5f3))


### Features

* Add new table to artemis excel ([#321](https://github.com/unlockre/keycomps/issues/321)) ([0575a46](https://github.com/unlockre/keycomps/commit/0575a461955a47a5dda0e558a4b56a5ca4346e85))
* Add property name and ellipsis ([#307](https://github.com/unlockre/keycomps/issues/307)) ([3fd2019](https://github.com/unlockre/keycomps/commit/3fd2019f257a89108620870a665e38f9376e6393))
* Add review half stars ([#308](https://github.com/unlockre/keycomps/issues/308)) ([a3c36eb](https://github.com/unlockre/keycomps/commit/a3c36eb3ee4b1e6bc393c117e12a0e09da5b4fbc))
* Add source link ([#312](https://github.com/unlockre/keycomps/issues/312)) ([c3ad942](https://github.com/unlockre/keycomps/commit/c3ad94278528474edfba483ff906cc272ed8adbe))
* Extrapolate points when 0 and add empty state ([#315](https://github.com/unlockre/keycomps/issues/315)) ([1dd52f9](https://github.com/unlockre/keycomps/commit/1dd52f9ad82de167a45808c1589993ba0dc4f640))
* Hide tooltip on cell hover leave ([#316](https://github.com/unlockre/keycomps/issues/316)) ([d9dab19](https://github.com/unlockre/keycomps/commit/d9dab19c40a47eec68cbcd32da0a30cd1e50764c))
* Show last update date ([#309](https://github.com/unlockre/keycomps/issues/309)) ([d20e2db](https://github.com/unlockre/keycomps/commit/d20e2db3af4f478310e655a43914953bc9f6b972))
* Use export pdf pkg ([#318](https://github.com/unlockre/keycomps/issues/318)) ([fa31df5](https://github.com/unlockre/keycomps/commit/fa31df5b1658f11356471d2bd747f9c3c7ec1b73))

# [1.5.0](https://github.com/unlockre/keycomps/compare/v1.4.1...v1.5.0) (2024-10-15)


### Bug Fixes

* Correct reviews graph lines not matching values ([#302](https://github.com/unlockre/keycomps/issues/302)) ([13a3d43](https://github.com/unlockre/keycomps/commit/13a3d43aaf85e8927945cb6cd078cd2edc27b572))
* Fix condition to show topic select and selects onChange to avoid no value selection ([#301](https://github.com/unlockre/keycomps/issues/301)) ([f821c6b](https://github.com/unlockre/keycomps/commit/f821c6b13b12aafa61776fa687b336a5f72fafa1))
* Handle date range selector outside click ([#285](https://github.com/unlockre/keycomps/issues/285)) ([a8a549c](https://github.com/unlockre/keycomps/commit/a8a549c2230399d82cc34936b1c6b1c78cb708c2))
* Improve copies ([#300](https://github.com/unlockre/keycomps/issues/300)) ([70726d3](https://github.com/unlockre/keycomps/commit/70726d3ec86c49cc507732f16894fc19d426647d))
* Small UI fixes ([#288](https://github.com/unlockre/keycomps/issues/288)) ([08f02cf](https://github.com/unlockre/keycomps/commit/08f02cfd78ce105b4868b70145e8e6328e97e4c3))


### Features

* Add CompPropertyDrawer to CompsTab ([#287](https://github.com/unlockre/keycomps/issues/287)) ([0f43dfe](https://github.com/unlockre/keycomps/commit/0f43dfe8f55a0661aa0259653e5412da0e3db8fc))
* Add markdown to Reviews Summary ([#296](https://github.com/unlockre/keycomps/issues/296)) ([f5f3412](https://github.com/unlockre/keycomps/commit/f5f3412272559750382a19132c3d4de2b804386c))
* Add Property reviews  modal ([#264](https://github.com/unlockre/keycomps/issues/264)) ([33ed3d1](https://github.com/unlockre/keycomps/commit/33ed3d165b2b97e51def823b8243f596de5069a3))
* Add review graphics widget ([#292](https://github.com/unlockre/keycomps/issues/292)) ([d212a83](https://github.com/unlockre/keycomps/commit/d212a83d6593cbdd788167391945a42cbf65a290))
* Add tooltips ([#304](https://github.com/unlockre/keycomps/issues/304)) ([9ce6ae6](https://github.com/unlockre/keycomps/commit/9ce6ae69a9b02776318fa0cb5b859c5ac68f2d97))
* Add View More button to CompSetEditScreen ([#297](https://github.com/unlockre/keycomps/issues/297)) ([0516ccc](https://github.com/unlockre/keycomps/commit/0516ccce0983191fa3a30bf412b3e79132a3ca82)), closes [#293](https://github.com/unlockre/keycomps/issues/293)
* Improve ListingTable ([#298](https://github.com/unlockre/keycomps/issues/298)) ([4f71321](https://github.com/unlockre/keycomps/commit/4f71321460096ed719cad22dd1a26b617935d048))
* Keyreview color adjustments ([#299](https://github.com/unlockre/keycomps/issues/299)) ([136302b](https://github.com/unlockre/keycomps/commit/136302bb0386e7dbd2db80304459628c3b8d2ce0))
* Reviews tab UI fixes ([#291](https://github.com/unlockre/keycomps/issues/291)) ([3f7046a](https://github.com/unlockre/keycomps/commit/3f7046a46b456257616d8e0d0615f3c7d37da037))
* Sort columns by totalReviews ([#305](https://github.com/unlockre/keycomps/issues/305)) ([4402b47](https://github.com/unlockre/keycomps/commit/4402b471e8d0ec1897e4d2523dc1b64dbc67d955))
* Use aliases in review section ([#289](https://github.com/unlockre/keycomps/issues/289)) ([90e5a69](https://github.com/unlockre/keycomps/commit/90e5a692937db7a3b5385b36b3ad62fa174853ea))

## [1.4.1](https://github.com/unlockre/keycomps/compare/v1.4.0...v1.4.1) (2024-10-11)


### Bug Fixes

* Fix average yearBuilt and listings ([#290](https://github.com/unlockre/keycomps/issues/290)) ([af04fb3](https://github.com/unlockre/keycomps/commit/af04fb3832ab9accd97a15a62aec7e8622dc7f94))
* Fix rent changes formatting ([#294](https://github.com/unlockre/keycomps/issues/294)) ([cdb60f9](https://github.com/unlockre/keycomps/commit/cdb60f98d1a9501c57bad1f4bda26b5192e8f5e7))

# [1.4.0](https://github.com/unlockre/keycomps/compare/v1.3.4...v1.4.0) (2024-10-10)


### Bug Fixes

* Adjust api response mapping ([#284](https://github.com/unlockre/keycomps/issues/284)) ([ad0b63d](https://github.com/unlockre/keycomps/commit/ad0b63d9794bc94c54a49c14a865fe308f987b06))
* Bump clib version ([#263](https://github.com/unlockre/keycomps/issues/263)) ([aede690](https://github.com/unlockre/keycomps/commit/aede690bfbd96c97ac924c358c858969ecf30fb3))
* Handle no target property stats scenario ([#277](https://github.com/unlockre/keycomps/issues/277)) ([8cbc508](https://github.com/unlockre/keycomps/commit/8cbc508cbf3518ffaa90fa2c6008b3d8087102de))
* Headers background ([#283](https://github.com/unlockre/keycomps/issues/283)) ([5de0f48](https://github.com/unlockre/keycomps/commit/5de0f482c11c20dc1cc1482c6827823d95e54ca7))
* Move columns to domain ([#267](https://github.com/unlockre/keycomps/issues/267)) ([8b73148](https://github.com/unlockre/keycomps/commit/8b731480a7b351c9699cacb900462b8a1c0eff29))
* Show latest concession ([#273](https://github.com/unlockre/keycomps/issues/273)) ([eaf0e5f](https://github.com/unlockre/keycomps/commit/eaf0e5f5a63b9af5a8dba291780f8dfffef991b1))
* Support new stats endpoint ([#271](https://github.com/unlockre/keycomps/issues/271)) ([80a3783](https://github.com/unlockre/keycomps/commit/80a3783b5546bff8e04741f01f5ed9946af50694))


### Features

* Add comps stats modal ([#274](https://github.com/unlockre/keycomps/issues/274)) ([4d2e6e9](https://github.com/unlockre/keycomps/commit/4d2e6e922a2b4ad3c37ad041b78491c77f34b477))
* Add new range selector ([#280](https://github.com/unlockre/keycomps/issues/280)) ([5b88efe](https://github.com/unlockre/keycomps/commit/5b88efe20733539f2c404f1d64a188bec8e728c6))
* Add performance row ([#272](https://github.com/unlockre/keycomps/issues/272)) ([f5b37f7](https://github.com/unlockre/keycomps/commit/f5b37f7ba757c631ad4e905c9ee11e1e94be4807))
* Add reviews summary widget ([#268](https://github.com/unlockre/keycomps/issues/268)) ([fb4e33c](https://github.com/unlockre/keycomps/commit/fb4e33cd0f2957e8bd103baae5d6a3a60ed6a0ad))
* Add summary row comps tab ([#265](https://github.com/unlockre/keycomps/issues/265)) ([36b93f3](https://github.com/unlockre/keycomps/commit/36b93f30d6af0b26f3bf3322fc582c3d433e22dd))
* Adjust topics table styles ([#276](https://github.com/unlockre/keycomps/issues/276)) ([d50abea](https://github.com/unlockre/keycomps/commit/d50abea977182822de7a944ea4624c36aa7676a1))
* Change Comp Analysis look ([#275](https://github.com/unlockre/keycomps/issues/275)) ([833a148](https://github.com/unlockre/keycomps/commit/833a148a21e0325efc28e9d6db194385cc3a7f37))
* Change CompSetEditScreen look ([#270](https://github.com/unlockre/keycomps/issues/270)) ([713b919](https://github.com/unlockre/keycomps/commit/713b9194ec38f56cf4ee1a3ab1c289df13055ed2))
* Change HomeScreen look ([#266](https://github.com/unlockre/keycomps/issues/266)) ([06053d2](https://github.com/unlockre/keycomps/commit/06053d27baa101eb143eddb76f16467be682d33a))
* Extend application theme ([#278](https://github.com/unlockre/keycomps/issues/278)) ([3a9d2ff](https://github.com/unlockre/keycomps/commit/3a9d2fff1273d9562b2398cea03eec886dcc6751))
* Project creation and status polling ([#269](https://github.com/unlockre/keycomps/issues/269)) ([ac8c568](https://github.com/unlockre/keycomps/commit/ac8c568eb75f7d74ad1b23d53f63ebac16500c23))

## [1.3.4](https://github.com/unlockre/keycomps/compare/v1.3.3...v1.3.4) (2024-09-25)


### Bug Fixes

* Handle compsets with source property without rent info ([#260](https://github.com/unlockre/keycomps/issues/260)) ([51f1895](https://github.com/unlockre/keycomps/commit/51f1895318ba5791ef1446940e076c9cbbd137b9))

## [1.3.3](https://github.com/unlockre/keycomps/compare/v1.3.2...v1.3.3) (2024-09-24)


### Bug Fixes

* Add precentage symbol in artemis excel ([#258](https://github.com/unlockre/keycomps/issues/258)) ([5ae7890](https://github.com/unlockre/keycomps/commit/5ae7890aac79e67212b86f9bb35a66141e5d5140))

## [1.3.2](https://github.com/unlockre/keycomps/compare/v1.3.1...v1.3.2) (2024-09-24)


### Bug Fixes

* Use formulas for comps rent psf ([#256](https://github.com/unlockre/keycomps/issues/256)) ([66a623a](https://github.com/unlockre/keycomps/commit/66a623ad4de08d422e5e5ed0139ae54b67f931d8))

## [1.3.1](https://github.com/unlockre/keycomps/compare/v1.3.0...v1.3.1) (2024-09-24)


### Bug Fixes

* Remove unused function ([#254](https://github.com/unlockre/keycomps/issues/254)) ([2df1f33](https://github.com/unlockre/keycomps/commit/2df1f33002a31c221041778f48881030f90b4fae))
* Use formula for avg and remove dashed line ([#252](https://github.com/unlockre/keycomps/issues/252)) ([d36571c](https://github.com/unlockre/keycomps/commit/d36571c61aeb456d52270ecf7cc87727ba349329))

# [1.3.0](https://github.com/unlockre/keycomps/compare/v1.2.1...v1.3.0) (2024-09-23)


### Bug Fixes

* Artemis excel total formula ([#248](https://github.com/unlockre/keycomps/issues/248)) ([3aed296](https://github.com/unlockre/keycomps/commit/3aed296c5982c5fcaaa717c20b5f5b01ea107cea))
* Remove borders and colors ([#250](https://github.com/unlockre/keycomps/issues/250)) ([0d5b44d](https://github.com/unlockre/keycomps/commit/0d5b44d73e282218722f843bd30c8c366384cdd5))
* Use formulaes for artemis excel inplace rent row ([#249](https://github.com/unlockre/keycomps/issues/249)) ([0714eaa](https://github.com/unlockre/keycomps/commit/0714eaa528894a09ea074814daaf402d8e280ba8))


### Features

* Add artemis organization id and custom export ([#245](https://github.com/unlockre/keycomps/issues/245)) ([9c988d7](https://github.com/unlockre/keycomps/commit/9c988d78e681d04fe9fea8a146ed2a374fd39186))

## [1.2.1](https://github.com/unlockre/keycomps/compare/v1.2.0...v1.2.1) (2024-09-23)


### Bug Fixes

* Artemis excel formatting improvements ([#244](https://github.com/unlockre/keycomps/issues/244)) ([4a5aa83](https://github.com/unlockre/keycomps/commit/4a5aa839f0bcc97319441634cac000d5c1bcb2c2))

# [1.2.0](https://github.com/unlockre/keycomps/compare/v1.1.0...v1.2.0) (2024-09-20)


### Bug Fixes

* Artemis excel improvements ([#241](https://github.com/unlockre/keycomps/issues/241)) ([e077aa5](https://github.com/unlockre/keycomps/commit/e077aa50d2e9cc38b60137b74b8c56d5206c4493))
* Remove gray variants ([#240](https://github.com/unlockre/keycomps/issues/240)) ([ff6fe48](https://github.com/unlockre/keycomps/commit/ff6fe488e005c5f157b074f360abc9312aa7fcd2))


### Features

* Add comps by topic section ([#239](https://github.com/unlockre/keycomps/issues/239)) ([901f77b](https://github.com/unlockre/keycomps/commit/901f77bafdc21666829c7082d9e65f38da228a12))
* Add Keyreview API client ([#236](https://github.com/unlockre/keycomps/issues/236)) ([f0c65ef](https://github.com/unlockre/keycomps/commit/f0c65ef9a0558a23fac9797cea5a1473e8511617))
* Add performance widget ([#238](https://github.com/unlockre/keycomps/issues/238)) ([518d38a](https://github.com/unlockre/keycomps/commit/518d38a59ff17c7d94917e8106df5766a928b15e))

# [1.1.0](https://github.com/unlockre/keycomps/compare/v1.0.2...v1.1.0) (2024-09-17)


### Bug Fixes

* Handle no data in artemis excel ([#234](https://github.com/unlockre/keycomps/issues/234)) ([3c7b072](https://github.com/unlockre/keycomps/commit/3c7b07292ecf9a266c2b0df8ac82741c213120e2))
* Update Total SQFT label and column ([#231](https://github.com/unlockre/keycomps/issues/231)) ([5ad54c8](https://github.com/unlockre/keycomps/commit/5ad54c8e5a449a6aacd57bcc09b04ac1e7b4682a))


### Features

* Add artemis excel ([#233](https://github.com/unlockre/keycomps/issues/233)) ([b880efb](https://github.com/unlockre/keycomps/commit/b880efb857cf23d1cb7937db2f9c222cee8d8778))
* Update userback to support whitelist ([#228](https://github.com/unlockre/keycomps/issues/228)) ([dba845c](https://github.com/unlockre/keycomps/commit/dba845c6e5d3449b714bcdc1ab37661ab11942bd))

## [1.0.2](https://github.com/unlockre/keycomps/compare/v1.0.1...v1.0.2) (2024-09-09)


### Bug Fixes

* Fix concessions fetching ([#224](https://github.com/unlockre/keycomps/issues/224)) ([45d268c](https://github.com/unlockre/keycomps/commit/45d268ce47bc3f911cd141a399917616bdee2e00))

## [1.0.1](https://github.com/unlockre/keycomps/compare/v1.0.0...v1.0.1) (2024-09-05)


### Bug Fixes

* Update @unlockre/property-assets-api-tools ([#222](https://github.com/unlockre/keycomps/issues/222)) ([a12f63b](https://github.com/unlockre/keycomps/commit/a12f63b5952ccd71d7bf8729f675f1a1567811ff))

# 1.0.0 (2024-09-02)


### Bug Fixes

* Add a clear button to the comp set search filter ([#39](https://github.com/unlockre/keycomps/issues/39)) ([1da3596](https://github.com/unlockre/keycomps/commit/1da359648ee123341f2c1b700dda8c27a54ec193))
* Add align property to ListingsTable and CompsTable columns ([#89](https://github.com/unlockre/keycomps/issues/89)) ([62b3322](https://github.com/unlockre/keycomps/commit/62b332216a17705a4a44d9af2cdb87fe8c8cfa5f))
* Add base path to export url ([#67](https://github.com/unlockre/keycomps/issues/67)) ([a41ba82](https://github.com/unlockre/keycomps/commit/a41ba821715092e2362b7fae9da9af5864fe788b))
* Add base property to listings ([#74](https://github.com/unlockre/keycomps/issues/74)) ([5ad1fb9](https://github.com/unlockre/keycomps/commit/5ad1fb9308b4ee8f53604ec66bc198aa50bf7247))
* Add Change column to Comps CSV ([#115](https://github.com/unlockre/keycomps/issues/115)) ([2e11729](https://github.com/unlockre/keycomps/commit/2e1172952085b35fe2318d60401003717f878362))
* Add change column to comps table ([#77](https://github.com/unlockre/keycomps/issues/77)) ([97eab1c](https://github.com/unlockre/keycomps/commit/97eab1c1cd5cf49b7f0de72ff5fbd96cd7a1fa45))
* Add comp index icon to table ([#95](https://github.com/unlockre/keycomps/issues/95)) ([9be6b2f](https://github.com/unlockre/keycomps/commit/9be6b2f2b2a78e9a143fa73252ed83f753dc8cc9))
* Add correct formatter to rent columns ([#102](https://github.com/unlockre/keycomps/issues/102)) ([5305a20](https://github.com/unlockre/keycomps/commit/5305a20014fc13e8806de8abb0fd1051fc935fc6))
* Add empty state to CompsTable ([#106](https://github.com/unlockre/keycomps/issues/106)) ([a40da43](https://github.com/unlockre/keycomps/commit/a40da4379f381581191faa3519bc4ea66068510c))
* Add printing ready to comps tab view ([#64](https://github.com/unlockre/keycomps/issues/64)) ([c57c267](https://github.com/unlockre/keycomps/commit/c57c2672f2ddbc7f3c816e47bb627386ca259b86))
* Add property border in comps edition ([#152](https://github.com/unlockre/keycomps/issues/152)) ([26d3037](https://github.com/unlockre/keycomps/commit/26d303740ed8505112159316d035f9742a86c712))
* Add rent info to base property on comps table ([#91](https://github.com/unlockre/keycomps/issues/91)) ([3d93f9f](https://github.com/unlockre/keycomps/commit/3d93f9fd165d92e1d0fb661f62d21c35038619cf))
* Add rent summary relation to manually added comp ([#119](https://github.com/unlockre/keycomps/issues/119)) ([c078fcb](https://github.com/unlockre/keycomps/commit/c078fcb78296190a4653c24bc68e1ca3bc311dd8))
* Add safe unit rent summary usage ([#161](https://github.com/unlockre/keycomps/issues/161)) ([063c805](https://github.com/unlockre/keycomps/commit/063c805e358a7b3127f39f2b02e0fe877b4c478e))
* Always enable the "New Comp Set" button ([#31](https://github.com/unlockre/keycomps/issues/31)) ([c23b185](https://github.com/unlockre/keycomps/commit/c23b1855e5c3be80feb39c7415dc2763d51979a4)), closes [/github.com/unlockre/keycomps/pull/31#discussion_r1676321758](https://github.com//github.com/unlockre/keycomps/pull/31/issues/discussion_r1676321758)
* Avg effective rent comp column ([#173](https://github.com/unlockre/keycomps/issues/173)) ([1b2799f](https://github.com/unlockre/keycomps/commit/1b2799f77bda889521ce16fe17fe9422f8226db3))
* Capitalize the Last Modified value ([#40](https://github.com/unlockre/keycomps/issues/40)) ([51e7780](https://github.com/unlockre/keycomps/commit/51e77801a2a002322f71767c99b3c5d359c74c00))
* Change default action in comp set deletion modal ([#111](https://github.com/unlockre/keycomps/issues/111)) ([29e3754](https://github.com/unlockre/keycomps/commit/29e3754ab8fba171685a86d75e9d0759cf1c555d))
* Change effective rent values on PSF/Unit switch ([#141](https://github.com/unlockre/keycomps/issues/141)) ([8c1c1f9](https://github.com/unlockre/keycomps/commit/8c1c1f9a4f2c308800f078652478a4e6e8573465))
* Change navbar width ([#209](https://github.com/unlockre/keycomps/issues/209)) ([d4f066e](https://github.com/unlockre/keycomps/commit/d4f066e156cfe86f7cf26181d534cf31bd2c05d2))
* Change nulleable PSF ([#129](https://github.com/unlockre/keycomps/issues/129)) ([e794e7d](https://github.com/unlockre/keycomps/commit/e794e7d29aa533183b3cb79b7c13dfb2a4985f42))
* Change picker event handler to enable stop propagation ([#57](https://github.com/unlockre/keycomps/issues/57)) ([77d248e](https://github.com/unlockre/keycomps/commit/77d248e39b7175fe6e265029318ed9e3294518f0))
* Change stg keyhub deploy url ([864f1d1](https://github.com/unlockre/keycomps/commit/864f1d1790eece43a76efb3d96a3c549b5111881))
* Change the stickyness of the home header ([#24](https://github.com/unlockre/keycomps/issues/24)) ([81610c5](https://github.com/unlockre/keycomps/commit/81610c5b79bffbe8715031ad9f18610c343c1d1a))
* Check CompsMap width and padding ([#193](https://github.com/unlockre/keycomps/issues/193)) ([00bbbb3](https://github.com/unlockre/keycomps/commit/00bbbb3c033271e5a219b3431ba2defe89d82a88))
* Disable cancel rename on click outside modal ([#116](https://github.com/unlockre/keycomps/issues/116)) ([c179cf1](https://github.com/unlockre/keycomps/commit/c179cf1da5e9c0331aca9f3852cc5c29a3f25103))
* Disable New Comp Set button on click ([#104](https://github.com/unlockre/keycomps/issues/104)) ([482e84d](https://github.com/unlockre/keycomps/commit/482e84de6f81cfa1cab04aa1ea5ec1edf3f5340b))
* Disable rent history api call when there are no properties ([#88](https://github.com/unlockre/keycomps/issues/88)) ([81250ac](https://github.com/unlockre/keycomps/commit/81250ac5bd05ebb6fcfc6da3d7d79b0b2c006278))
* Disable rent history call ([#82](https://github.com/unlockre/keycomps/issues/82)) ([34604bb](https://github.com/unlockre/keycomps/commit/34604bbe31b9bf2b1b2d1da2881450dab0b803b4))
* Edit comps table padding and alignment improvements ([#54](https://github.com/unlockre/keycomps/issues/54)) ([a1f4345](https://github.com/unlockre/keycomps/commit/a1f434517fb1486ee587d60c46dc4c430fb7cf11))
* Fetch UnitRentSummary relation for all comps ([#90](https://github.com/unlockre/keycomps/issues/90)) ([09500fe](https://github.com/unlockre/keycomps/commit/09500fef674f56461c4c1369fbb402fb5c4c8fef))
* Fix amenity and concession table column widths ([#49](https://github.com/unlockre/keycomps/issues/49)) ([5439bb7](https://github.com/unlockre/keycomps/commit/5439bb751f51a472a8edc837f3bf6b4c2642fa5d))
* Fix amplitudeClient sourceName ([#36](https://github.com/unlockre/keycomps/issues/36)) ([23bbbba](https://github.com/unlockre/keycomps/commit/23bbbba099089ea614357bbf5dc6724dc6a1b5ea))
* Fix avgDaysOnMarketCompTableColumn formatting ([#96](https://github.com/unlockre/keycomps/issues/96)) ([b29fc33](https://github.com/unlockre/keycomps/commit/b29fc3339d366fd5d4b9532701dcd469ab700377))
* Fix comp columns ([#176](https://github.com/unlockre/keycomps/issues/176)) ([6ef45f7](https://github.com/unlockre/keycomps/commit/6ef45f75b07058a7c60dcdd800a055876528bb26))
* Fix Comp Set casing and hide similarity score for base property ([#182](https://github.com/unlockre/keycomps/issues/182)) ([795e205](https://github.com/unlockre/keycomps/commit/795e20517d124f076c0bdc21ed996407a3c9300f))
* Fix comps map ([#180](https://github.com/unlockre/keycomps/issues/180)) ([f2e3b96](https://github.com/unlockre/keycomps/commit/f2e3b96660cdeec7ffbb6259d55d7a1ae001fee6))
* Fix comps table metrics ([#45](https://github.com/unlockre/keycomps/issues/45)) ([c730b98](https://github.com/unlockre/keycomps/commit/c730b98c32a055adabcc3d5dad7e1a8e06fcea27))
* Fix deploy workflow configuration ([#213](https://github.com/unlockre/keycomps/issues/213)) ([10975b1](https://github.com/unlockre/keycomps/commit/10975b1ec6e711640b46400164ef2f52fe608046))
* Fix formatting issues ([#68](https://github.com/unlockre/keycomps/issues/68)) ([461a48f](https://github.com/unlockre/keycomps/commit/461a48f38054356af5c1da0c0abf41f33f0b6034))
* Fix getProperty unitRentSummaries retrieval ([#155](https://github.com/unlockre/keycomps/issues/155)) ([a603709](https://github.com/unlockre/keycomps/commit/a603709a717d52d4f9c6ef9d30ea12fec3f13450))
* Fix properties historicalRentSummaries fetching ([#159](https://github.com/unlockre/keycomps/issues/159)) ([49f68fa](https://github.com/unlockre/keycomps/commit/49f68fa36436d020dbe74f456680907bef851cfb))
* Fix relation utils ([#11](https://github.com/unlockre/keycomps/issues/11)) ([fb41a18](https://github.com/unlockre/keycomps/commit/fb41a189c4e22154f46d449d5fae8d80568495a6))
* Fix sentry and trpc ([#2](https://github.com/unlockre/keycomps/issues/2)) ([9a39d4e](https://github.com/unlockre/keycomps/commit/9a39d4e57fdf2e2d72cd234ba49dd3a59ee4f92e))
* Fix some stuff ([#17](https://github.com/unlockre/keycomps/issues/17)) ([42bf86f](https://github.com/unlockre/keycomps/commit/42bf86fc3470a125de1fd8996082f84c8ca57921))
* Fix typo ([#37](https://github.com/unlockre/keycomps/issues/37)) ([ce65b10](https://github.com/unlockre/keycomps/commit/ce65b10eae4baa834116675cc446e55888346cb9))
* General table fixes ([#76](https://github.com/unlockre/keycomps/issues/76)) ([a47e6e3](https://github.com/unlockre/keycomps/commit/a47e6e390f479951d59226c650b7f3d35b4ebb30))
* Handle API error when fetching historical rent summaries ([#122](https://github.com/unlockre/keycomps/issues/122)) ([01a45f2](https://github.com/unlockre/keycomps/commit/01a45f21d993a1cb29b5189f7c7db1a38cd46ef2))
* Handle invalid rent api response ([#190](https://github.com/unlockre/keycomps/issues/190)) ([1d43359](https://github.com/unlockre/keycomps/commit/1d43359438279785b81a4d3eb9348d6618dadf61))
* Handle optional rent PSF value on graph ([#135](https://github.com/unlockre/keycomps/issues/135)) ([2561957](https://github.com/unlockre/keycomps/commit/2561957554075c5d4ee560b297aa4da938b7f0f3))
* Hide concession table cell header info icon in print view ([#56](https://github.com/unlockre/keycomps/issues/56)) ([ea86a42](https://github.com/unlockre/keycomps/commit/ea86a422ed65d1a178e726d1ec7770808801bd5e))
* Hide listings charts ([#92](https://github.com/unlockre/keycomps/issues/92)) ([1cd1c54](https://github.com/unlockre/keycomps/commit/1cd1c54a9377c9e8c0bdb9c1adacf4a580051845))
* Home style improvements ([#44](https://github.com/unlockre/keycomps/issues/44)) ([c504cba](https://github.com/unlockre/keycomps/commit/c504cba1d2566175a39a325314728a1dd615c2ed))
* Improve WAVG calculation ([#170](https://github.com/unlockre/keycomps/issues/170)) ([90c3572](https://github.com/unlockre/keycomps/commit/90c357244b454e4c8eccc88121e553b11682b806))
* Map location ([#112](https://github.com/unlockre/keycomps/issues/112)) ([0780660](https://github.com/unlockre/keycomps/commit/0780660353970a1fec50c7c0c678c28609be162e))
* Multiply by 100 "Change" column value ([#114](https://github.com/unlockre/keycomps/issues/114)) ([bd08293](https://github.com/unlockre/keycomps/commit/bd0829385f1fd653825cc6b746733c4135ed5b57))
* Prevent empty comps filter request ([#149](https://github.com/unlockre/keycomps/issues/149)) ([b578b56](https://github.com/unlockre/keycomps/commit/b578b56252e1e93faea3781593fee0a88e3e5f3f))
* Prevent not found metrics from breaking app ([#73](https://github.com/unlockre/keycomps/issues/73)) ([e39e59a](https://github.com/unlockre/keycomps/commit/e39e59ab9d30867c6f4aff3b9299e56d5c84d1a4))
* Prevent space only requests ([#208](https://github.com/unlockre/keycomps/issues/208)) ([e53c018](https://github.com/unlockre/keycomps/commit/e53c018423ede9e3fb1c10e40e44451258a52c02))
* Property finder z-index regression ([#29](https://github.com/unlockre/keycomps/issues/29)) ([17db3cf](https://github.com/unlockre/keycomps/commit/17db3cf8871ef7203b5c6514285e33c850e5fa14))
* Remove occupancy ([#78](https://github.com/unlockre/keycomps/issues/78)) ([1a78f34](https://github.com/unlockre/keycomps/commit/1a78f344f32622fd32c1eeb45911c932385efffa))
* Remove the extra margings from the comp set edit content area ([#43](https://github.com/unlockre/keycomps/issues/43)) ([74a44a1](https://github.com/unlockre/keycomps/commit/74a44a1bb5becfba718407c380a26935fde7a777))
* Rent averages ([#134](https://github.com/unlockre/keycomps/issues/134)) ([9925b39](https://github.com/unlockre/keycomps/commit/9925b391b98172264630e6092966192aed7e9875))
* Replace .toSorted() with .sort() ([#214](https://github.com/unlockre/keycomps/issues/214)) ([f1ec1ad](https://github.com/unlockre/keycomps/commit/f1ec1ad7a09439b2f93d6bfbc62aea2dcea607e3))
* Replace "-" for em dash for empty values ([#33](https://github.com/unlockre/keycomps/issues/33)) ([dd15b79](https://github.com/unlockre/keycomps/commit/dd15b79db314268164ca2d41cbe21c62c7fa436e))
* Round avg days on market ([#140](https://github.com/unlockre/keycomps/issues/140)) ([b487be0](https://github.com/unlockre/keycomps/commit/b487be0e71bde44267b1f08698b9d273fc7c5588))
* Set max weeks to show on listing charts ([#101](https://github.com/unlockre/keycomps/issues/101)) ([e31608e](https://github.com/unlockre/keycomps/commit/e31608ece25fd48235ed32c07251d7b644a86b9c))
* Show a placeholder in the home table filter ([#25](https://github.com/unlockre/keycomps/issues/25)) ([866e1f8](https://github.com/unlockre/keycomps/commit/866e1f880df3ca234b11522bd50547f6016bee4e))
* Show border only in selected comps table ([#153](https://github.com/unlockre/keycomps/issues/153)) ([e5dad30](https://github.com/unlockre/keycomps/commit/e5dad30f69b83038dac6437333d806ba5890d44c))
* Show dash in each amenity column when there's no amenity data ([#75](https://github.com/unlockre/keycomps/issues/75)) ([3257bf3](https://github.com/unlockre/keycomps/commit/3257bf313f402c952666d8736d387fbc7f363579))
* Show icons for the home table actions ([#27](https://github.com/unlockre/keycomps/issues/27)) ([6bc3d71](https://github.com/unlockre/keycomps/commit/6bc3d7102a1899a8da8f4e9e7b7f914892ba210c))
* Show last seen date even if it's today ([#143](https://github.com/unlockre/keycomps/issues/143)) ([f0b7052](https://github.com/unlockre/keycomps/commit/f0b70526b0edc995ee007cee06f08acdde93c56e))
* Show rent graph only if there are comps ([#136](https://github.com/unlockre/keycomps/issues/136)) ([e720a36](https://github.com/unlockre/keycomps/commit/e720a36e4ffe5f421fbcaf9f0e8dae4a936f6eb4))
* Show TableEmptyState if no listings ([#46](https://github.com/unlockre/keycomps/issues/46)) ([136d934](https://github.com/unlockre/keycomps/commit/136d93443c21454543633129fe3e656769024ee8))
* Table improvements ([#113](https://github.com/unlockre/keycomps/issues/113)) ([4fb638a](https://github.com/unlockre/keycomps/commit/4fb638a0743f685c1fb1db7e8c91d52ca04303cb))
* Undefined listing square footage ([#126](https://github.com/unlockre/keycomps/issues/126)) ([7af48a3](https://github.com/unlockre/keycomps/commit/7af48a35942692e618570dc830977acee03f212d))
* Update columns header and property image placeholder ([#107](https://github.com/unlockre/keycomps/issues/107)) ([98cc7ec](https://github.com/unlockre/keycomps/commit/98cc7ec0e1a51c8eee1cbe8d2b2e8bcc955bcff5))
* Update components library version ([#163](https://github.com/unlockre/keycomps/issues/163)) ([f930b07](https://github.com/unlockre/keycomps/commit/f930b0770c69d382b8ff99cd6382c8c8e206e132))
* Update days on market date calculation ([#87](https://github.com/unlockre/keycomps/issues/87)) ([aeb1575](https://github.com/unlockre/keycomps/commit/aeb15756bb6d6f0f301364cabb9f286bdb56b95e))
* Use Date for rent graph scale ([#79](https://github.com/unlockre/keycomps/issues/79)) ([447e2d2](https://github.com/unlockre/keycomps/commit/447e2d2397a1f429375ad7a04938aca8af6b3d0a))
* Use dot as decimal separator ([#145](https://github.com/unlockre/keycomps/issues/145)) ([ae343ca](https://github.com/unlockre/keycomps/commit/ae343ca7a77b3006129b91355641602ac46d15cc))
* Use rent history relation ([#133](https://github.com/unlockre/keycomps/issues/133)) ([fdc003a](https://github.com/unlockre/keycomps/commit/fdc003a3ed97419d6029baaeecdcad4e3cf564b9))
* Use secondary variant for the similarity score ([#50](https://github.com/unlockre/keycomps/issues/50)) ([0da0667](https://github.com/unlockre/keycomps/commit/0da06675e3134571d0fa09ac01ee61506064ab00))
* Use source property to calculate weighted average ([#162](https://github.com/unlockre/keycomps/issues/162)) ([b773915](https://github.com/unlockre/keycomps/commit/b77391528712aa414e91774092f51a32632bbd5c))
* Use unit mix number of units ([#166](https://github.com/unlockre/keycomps/issues/166)) ([b30bb1f](https://github.com/unlockre/keycomps/commit/b30bb1f0a8cd9be9e75bbfcdfca76379994cae8a))
* Use unit mix to calculate wavg ([#212](https://github.com/unlockre/keycomps/issues/212)) ([95dc0e5](https://github.com/unlockre/keycomps/commit/95dc0e5c1c9aeff5e1db7589dc083c09a27d5f90))
* Use URL date query params in Comps tab ([#65](https://github.com/unlockre/keycomps/issues/65)) ([5ad78c0](https://github.com/unlockre/keycomps/commit/5ad78c0934be9198c0628f367e0873bb175a55cb))
* Wording ([#197](https://github.com/unlockre/keycomps/issues/197)) ([bfd0894](https://github.com/unlockre/keycomps/commit/bfd08943a67f7704b7b733fa7f872bae10e63559))


### Features

* Add amenity table ([#16](https://github.com/unlockre/keycomps/issues/16)) ([c365bfd](https://github.com/unlockre/keycomps/commit/c365bfdd8ab8ac55bed173fe436ef5445034e526))
* Add column type and create to domain ([#30](https://github.com/unlockre/keycomps/issues/30)) ([6db42a2](https://github.com/unlockre/keycomps/commit/6db42a2e3ea438099dbfc9b58e05c1ccd65355c9))
* Add comps tab table ([#26](https://github.com/unlockre/keycomps/issues/26)) ([ea5948b](https://github.com/unlockre/keycomps/commit/ea5948bd3d2596883aa9c6f1e7908a5bb4c95fbe))
* Add comps table, map, and drawer ([#8](https://github.com/unlockre/keycomps/issues/8)) ([9b12a8d](https://github.com/unlockre/keycomps/commit/9b12a8d5511184c0102fac30551b5ac65ad1f1c9))
* Add CompSetEditScreen ([#4](https://github.com/unlockre/keycomps/issues/4)) ([0094106](https://github.com/unlockre/keycomps/commit/00941066a777fcc23ebdcdc96b83c2dc2eea4306))
* Add CompSetEditScreen missing features ([#22](https://github.com/unlockre/keycomps/issues/22)) ([ff9d56c](https://github.com/unlockre/keycomps/commit/ff9d56cb7ed213fb9fa62a85852780528953f763))
* Add concession table ([#21](https://github.com/unlockre/keycomps/issues/21)) ([d092150](https://github.com/unlockre/keycomps/commit/d09215060614243d163b5349f21656eaa4a14fda))
* Add concessions repository ([#34](https://github.com/unlockre/keycomps/issues/34)) ([1b03302](https://github.com/unlockre/keycomps/commit/1b03302788e204efbef898e942f428329a249479))
* Add custom distance zipcodes ([#146](https://github.com/unlockre/keycomps/issues/146)) ([a082187](https://github.com/unlockre/keycomps/commit/a08218768457c2e9030b8fd396d2b7979984093e))
* Add discriminated pet fees in csv export ([#35](https://github.com/unlockre/keycomps/issues/35)) ([33335a2](https://github.com/unlockre/keycomps/commit/33335a225d03fe7bc363b1f157fcc2bd82f0a928))
* Add effectiveRent column to ListingsTable ([#211](https://github.com/unlockre/keycomps/issues/211)) ([6d21b06](https://github.com/unlockre/keycomps/commit/6d21b067a4ae36bc1992598a17552baad2b1da0c))
* Add excel export for intercapital ([#154](https://github.com/unlockre/keycomps/issues/154)) ([5ab9a3f](https://github.com/unlockre/keycomps/commit/5ab9a3ffc0b804b933f236620435fa5d9c3d11cd))
* Add export pdf action ([#19](https://github.com/unlockre/keycomps/issues/19)) ([86b2407](https://github.com/unlockre/keycomps/commit/86b24070dbf550d4a71d5003470a11900c60dd08))
* Add Export Table to Listings section ([#51](https://github.com/unlockre/keycomps/issues/51)) ([151524c](https://github.com/unlockre/keycomps/commit/151524cecbdae23a6e58b9eb213f264ef56f0cac))
* Add ExtendedCompsSection ([#84](https://github.com/unlockre/keycomps/issues/84)) ([7c34e89](https://github.com/unlockre/keycomps/commit/7c34e89789ab432bbc8831b9058e51b8e5902597))
* Add fees table ([#14](https://github.com/unlockre/keycomps/issues/14)) ([5a0f3f4](https://github.com/unlockre/keycomps/commit/5a0f3f455bc138c4555afdd95bbbeaf483cccbdb))
* Add historical rent graph ([#61](https://github.com/unlockre/keycomps/issues/61)) ([3ace822](https://github.com/unlockre/keycomps/commit/3ace822f57c458f3ad4ccb3562af07d4079ac412))
* Add historical rent summary relation ([#100](https://github.com/unlockre/keycomps/issues/100)) ([1286971](https://github.com/unlockre/keycomps/commit/1286971e102e188a186ac0bfdba42f4f20abfbbc))
* Add HomeScreen ([#1](https://github.com/unlockre/keycomps/issues/1)) ([592ca80](https://github.com/unlockre/keycomps/commit/592ca80ebd85d9c4b45c5a173fe036df35f48ad8))
* Add listings charts ([#72](https://github.com/unlockre/keycomps/issues/72)) ([9144b5c](https://github.com/unlockre/keycomps/commit/9144b5c03a09138f17186fbed4a1f9d4220a158c))
* Add listings Future Availability chart ([#81](https://github.com/unlockre/keycomps/issues/81)) ([42ba8db](https://github.com/unlockre/keycomps/commit/42ba8db096681a03f46c76026a761d57d8ad13c8))
* Add ListingTable to ListingsTabView ([#32](https://github.com/unlockre/keycomps/issues/32)) ([166d6a4](https://github.com/unlockre/keycomps/commit/166d6a4c40b8f848e1085262e3e4dc02d0dd390a)), closes [#21](https://github.com/unlockre/keycomps/issues/21) [#43](https://github.com/unlockre/keycomps/issues/43) [#34](https://github.com/unlockre/keycomps/issues/34) [#21](https://github.com/unlockre/keycomps/issues/21) [#43](https://github.com/unlockre/keycomps/issues/43) [#34](https://github.com/unlockre/keycomps/issues/34)
* Add map to comps tab ([#23](https://github.com/unlockre/keycomps/issues/23)) ([13e0c06](https://github.com/unlockre/keycomps/commit/13e0c066ab498d6dc1a89c4fe9bde1acc688cd23))
* Add new units count columns ([#218](https://github.com/unlockre/keycomps/issues/218)) ([2b30a79](https://github.com/unlockre/keycomps/commit/2b30a7917093592a8dbddd8428f576cea7d50d38))
* Add Rent Change columns to Rent Insights ([#137](https://github.com/unlockre/keycomps/issues/137)) ([7e05d4c](https://github.com/unlockre/keycomps/commit/7e05d4c7b786a9f26ca8062921a57bd71e98210b))
* Add rent related domain relations ([#7](https://github.com/unlockre/keycomps/issues/7)) ([7b8e3fa](https://github.com/unlockre/keycomps/commit/7b8e3fa6849f4a40581c634f421567d21d9611e2))
* Add Reviews tab ([#202](https://github.com/unlockre/keycomps/issues/202)) ([a1321f1](https://github.com/unlockre/keycomps/commit/a1321f19360260ffb652b45eecc7390c60e44e99))
* Add tabs date filter ([#47](https://github.com/unlockre/keycomps/issues/47)) ([f85a514](https://github.com/unlockre/keycomps/commit/f85a514c418a7b3088c7106804e375729f26fd9e))
* Add Unit column to Listings table ([#132](https://github.com/unlockre/keycomps/issues/132)) ([a0c7a14](https://github.com/unlockre/keycomps/commit/a0c7a146028c55aa56a149934ea6d2e09ed8a4a5))
* Add UnitsRentSection ([#55](https://github.com/unlockre/keycomps/issues/55)) ([b7218ba](https://github.com/unlockre/keycomps/commit/b7218bac83b3c7e8a339bf15a04ddbc13dff18ff))
* Add userback ([#181](https://github.com/unlockre/keycomps/issues/181)) ([b0c0e4f](https://github.com/unlockre/keycomps/commit/b0c0e4f9bdbb9e574ad7511662884f4f2fd3e2cc))
* Add view compset layout ([#6](https://github.com/unlockre/keycomps/issues/6)) ([916a3b3](https://github.com/unlockre/keycomps/commit/916a3b3ff2b11a37e18773bb75edc7fd2f397a26))
* Autoscale pages ([#41](https://github.com/unlockre/keycomps/issues/41)) ([5a813eb](https://github.com/unlockre/keycomps/commit/5a813ebd4263e1a2b43d7bd34c80b5f3e3c3c9a8))
* Change date range of rent data for comp selector ([#196](https://github.com/unlockre/keycomps/issues/196)) ([702839b](https://github.com/unlockre/keycomps/commit/702839bdc6b1b0c245c9ce7d22e402503b8719d8))
* Change Listing's Last Seen columns header ([#142](https://github.com/unlockre/keycomps/issues/142)) ([bf6eba2](https://github.com/unlockre/keycomps/commit/bf6eba2134357f9d75a9f9bfecce5604be6d4c1f))
* Change max distance filter based on comp zipcode ([#144](https://github.com/unlockre/keycomps/issues/144)) ([0d19ec7](https://github.com/unlockre/keycomps/commit/0d19ec750e81458756ac0226afc44d58d188c38c))
* Concessions V2 ([#110](https://github.com/unlockre/keycomps/issues/110)) ([0a8c4fd](https://github.com/unlockre/keycomps/commit/0a8c4fdb7d3eab955e42495efbe6528b2673e964))
* Hide full table ([#99](https://github.com/unlockre/keycomps/issues/99)) ([978e1b7](https://github.com/unlockre/keycomps/commit/978e1b7dc12f601685c96b5e4f4b4c56846e599b))
* Improve domain logic and avoid unnecessary request ([#18](https://github.com/unlockre/keycomps/issues/18)) ([ec90e3d](https://github.com/unlockre/keycomps/commit/ec90e3d63caca9f5edec0be6b5a2d72d3f678f32))
* Integrate @unlockre/keyos-tools package ([#186](https://github.com/unlockre/keycomps/issues/186)) ([aead7d1](https://github.com/unlockre/keycomps/commit/aead7d16edd848f753a0cb5426f71d57a6688849))
* Integrate listing changes chart with listings data ([#69](https://github.com/unlockre/keycomps/issues/69)) ([38e7130](https://github.com/unlockre/keycomps/commit/38e7130014981f7dc2ff54c69ee9ecf92576c407))
* Make comp website a link ([#201](https://github.com/unlockre/keycomps/issues/201)) ([34d88c5](https://github.com/unlockre/keycomps/commit/34d88c5307f31d32686b8f810ae52a971a505a27))
* Navigate to CompSetViewScreen ([#12](https://github.com/unlockre/keycomps/issues/12)) ([0e5eae4](https://github.com/unlockre/keycomps/commit/0e5eae4de58c94e8056ac988efd8ba4ce680c7fd))
* Open compset edit view after creation ([#48](https://github.com/unlockre/keycomps/issues/48)) ([5552ef3](https://github.com/unlockre/keycomps/commit/5552ef3d042070b82b1db2c04e0da4f3216e7ae3))
* Print pdf in single page ([#86](https://github.com/unlockre/keycomps/issues/86)) ([b312541](https://github.com/unlockre/keycomps/commit/b312541dfb828783441a4ea0eac22c715a827572))
* Sort comps by last modified ([#60](https://github.com/unlockre/keycomps/issues/60)) ([d1ab0c6](https://github.com/unlockre/keycomps/commit/d1ab0c6d6743ddad6b706c209861b1914a865c51))
* Update domain schema and property repository ([#15](https://github.com/unlockre/keycomps/issues/15)) ([c1f244f](https://github.com/unlockre/keycomps/commit/c1f244fc73c793da034920152a1a52a958c9ce0e))
* Upgrade utils-userback version ([#198](https://github.com/unlockre/keycomps/issues/198)) ([3fd4c91](https://github.com/unlockre/keycomps/commit/3fd4c91f353d486e99b7fee14f5f2cc7ab016290))
* Use custom summary export ([#185](https://github.com/unlockre/keycomps/issues/185)) ([4381a0d](https://github.com/unlockre/keycomps/commit/4381a0dd1dfe4e320ab5765168e8f9145accd294))
* Use date in listings tabs and remove mock data ([#53](https://github.com/unlockre/keycomps/issues/53)) ([097c589](https://github.com/unlockre/keycomps/commit/097c589e033f9db829208b95363cbcbacaf86b4d))
* Use new userback hook ([#183](https://github.com/unlockre/keycomps/issues/183)) ([228193d](https://github.com/unlockre/keycomps/commit/228193d59d909058a86e3ca3637f76520db27f41))
